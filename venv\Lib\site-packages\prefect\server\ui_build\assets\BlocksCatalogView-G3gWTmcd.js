import{d as k,f as i,W as y,h as o,bF as m,i as b,c as _,a as f,o as d,j as a,k as t,m as c,cl as T,cm as v}from"./index-g6FNXcTE.js";import{u as g}from"./usePageTitle-DEr56mEx.js";const x=k({__name:"BlocksCatalogView",setup(C){const s=i(),l=y("blockTypeSlug"),n=o(()=>l.value?[l.value]:null),u=m(s.blockTypes.getBlockTypeBySlug,n),e=o(()=>u.response),p=o(()=>e.value?`Block Type: ${e.value.name}`:null);return g(p),(B,S)=>{const r=b("p-layout-default");return e.value?(d(),_(r,{key:0,class:"blocks-catalog-view"},{header:a(()=>[t(c(v),{"block-type":e.value},null,8,["block-type"])]),default:a(()=>[t(c(T),{"block-type":e.value},null,8,["block-type"])]),_:1})):f("",!0)}}});export{x as default};
//# sourceMappingURL=BlocksCatalogView-G3gWTmcd.js.map
