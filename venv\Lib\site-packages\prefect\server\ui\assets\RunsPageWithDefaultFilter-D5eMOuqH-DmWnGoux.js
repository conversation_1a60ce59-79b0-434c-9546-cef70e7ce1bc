import{d as u,s,c,a as l,w as p,o as m,r as i,p as f,b as d,e as v,P as h}from"./index-g6FNXcTE.js";const r=a=>{const{value:e,isCustom:t}=f();if(d(a.query)&&t.value)try{const o=v.map("SavedSearchFilter",e.value,"LocationQuery");return{...a,query:o}}catch(o){console.error(o)}return!0},R=u({beforeRouteEnter:r,beforeRouteUpdate:r,__name:"RunsPageWithDefaultFilter",props:{component:{type:Function}},setup(a){const e=a,t=s(null);function o(n){return h(n)}return p(e.component,()=>{o(e.component)?e.component().then(n=>{t.value=n.default}):t.value=e.component},{immediate:!0}),(n,y)=>t.value!==null?(m(),c(i(t.value),{key:0})):l("",!0)}});export{R as default};
//# sourceMappingURL=RunsPageWithDefaultFilter-D5eMOuqH-DmWnGoux.js.map
