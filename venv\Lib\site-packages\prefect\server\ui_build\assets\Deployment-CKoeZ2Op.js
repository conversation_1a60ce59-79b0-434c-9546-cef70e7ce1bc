import{d as E,W as V,h as r,u as z,f as A,g as P,aI as W,al as Z,bt as $,bu as f,bX as j,i as s,c,a as _,o as p,j as l,k as n,m as e,a6 as q,bz as b,q as G,F as L,B as M,bY as O,bZ as v,b_ as X,b$ as Y,c0 as H,c1 as J,c2 as K,c3 as Q,be as ee,c4 as te}from"./index-g6FNXcTE.js";import{u as ne}from"./usePageTitle-DEr56mEx.js";const se=E({__name:"Deployment",setup(le){const m=V("deploymentId"),i=r(()=>[m.value]),g=z(),h=A(),w={interval:3e5},d=P(h.deployments.getDeployment,[m.value],w),t=r(()=>d.response),x=r(()=>{var a,o;return[{label:"Details",hidden:W.xl},{label:"Runs"},{label:"Upcoming"},{label:"Parameters",hidden:(a=t.value)==null?void 0:a.deprecated},{label:"Configuration",hidden:(o=t.value)==null?void 0:o.deprecated},{label:"Description"}]}),u=Z("tab","Details"),{tabs:R}=$(x,u);function k(){g.push(ee.deployments())}const{filter:D}=f({deployments:{id:i},flowRuns:{state:{name:te.filter(a=>a!=="Scheduled")}}}),{filter:C}=f({sort:"START_TIME_ASC",deployments:{id:i},flowRuns:{state:{name:["Scheduled"]}}}),{flowRun:y}=j(()=>({deployments:{id:i.value}})),U=r(()=>t.value?`Deployment: ${t.value.name}`:"Deployment");return ne(U),(a,o)=>{const N=s("p-content"),T=s("p-heading"),F=s("p-divider"),I=s("p-tabs"),S=s("p-layout-well");return t.value?(p(),c(S,{key:0,class:"deployment"},{header:l(()=>[n(e(Q),{deployment:t.value,onUpdate:e(d).refresh,onDelete:k},null,8,["deployment","onUpdate"])]),well:l(()=>[n(e(v),{deployment:t.value,alternate:"",onUpdate:e(d).refresh},null,8,["deployment","onUpdate"])]),default:l(()=>[n(I,{selected:e(u),"onUpdate:selected":o[0]||(o[0]=B=>q(u)?u.value=B:null),tabs:e(R)},{description:l(()=>[n(N,{secondary:""},{default:l(()=>[t.value.deprecated?(p(),c(e(H),{key:0})):t.value.description?(p(),c(e(J),{key:1,description:t.value.description},null,8,["description"])):(p(),c(e(K),{key:2,deployment:t.value},null,8,["deployment"]))]),_:1})]),parameters:l(()=>[n(e(Y),{deployment:t.value},null,8,["deployment"])]),configuration:l(()=>[n(e(X),{deployment:t.value},null,8,["deployment"])]),details:l(()=>[n(e(v),{deployment:t.value,onUpdate:e(d).refresh},null,8,["deployment","onUpdate"])]),runs:l(()=>[e(y)?(p(),G(L,{key:0},[n(T,{heading:"6",class:"deployment__next-run"},{default:l(()=>o[1]||(o[1]=[M(" Next Run ")])),_:1,__:[1]}),n(e(O),{"flow-run":e(y)},null,8,["flow-run"]),n(F)],64)):_("",!0),n(e(b),{filter:e(D),selectable:"",prefix:"runs"},null,8,["filter"])]),upcoming:l(()=>[n(e(b),{filter:e(C),selectable:"",prefix:"upcoming"},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})):_("",!0)}}});export{se as default};
//# sourceMappingURL=Deployment-CKoeZ2Op.js.map
