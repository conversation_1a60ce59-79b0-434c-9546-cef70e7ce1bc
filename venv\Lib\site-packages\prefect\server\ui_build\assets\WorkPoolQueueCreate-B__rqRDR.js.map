{"version": 3, "file": "WorkPoolQueueCreate-B__rqRDR.js", "sources": ["../../src/pages/WorkPoolQueueCreate.vue"], "sourcesContent": ["<template>\n  <p-layout-default>\n    <template #header>\n      <PageHeadingWorkPoolQueueCreate :work-pool-name=\"workPoolName\" />\n    </template>\n\n    <WorkPoolQueueCreateForm :work-pool-name=\"workPoolName\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeadingWorkPoolQueueCreate, WorkPoolQueueCreateForm } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam } from '@prefecthq/vue-compositions'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const workPoolName = useRouteParam('workPoolName')\n\n  usePageTitle('Create Work Pool Queue')\n</script>"], "names": ["workPoolName", "useRouteParam", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingWorkPoolQueueCreate", "WorkPoolQueueCreateForm"], "mappings": "yMAeQ,MAAAA,EAAeC,EAAc,cAAc,EAEjD,OAAAC,EAAa,wBAAwB,mDAhBrCC,EAMmBC,EAAA,KAAA,CALN,SACT,IAAiE,CAAjEC,EAAiEC,EAAAC,CAAA,EAAA,CAAhC,iBAAgBD,EAAYN,CAAA,GAAA,KAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,aAG/D,IAA0D,CAA1DK,EAA0DC,EAAAE,CAAA,EAAA,CAAhC,iBAAgBF,EAAYN,CAAA,GAAA,KAAA,EAAA,CAAA,gBAAA,CAAA,CAAA"}