import{d as v,h as y,q as u,o as i,k as e,j as t,F as x,x as C,t as f,m as n,ds as B,J as w,i as l,c as F,dt as P,a6 as V,du as p,z as j}from"./index-g6FNXcTE.js";import{u as q}from"./usePageTitle-DEr56mEx.js";import{u as A}from"./usePrefectApi-iH-jzNq9.js";import"./api-CQZ_ymP4.js";import"./mapper-3GnQ1V33.js";const M={class:"settings-block"},T=v({__name:"SettingsCodeBlock",props:{engineSettings:{}},setup(d){const s=d,o=y(()=>Object.entries(s.engineSettings));return(g,c)=>(i(),u("div",M,[e(n(B),{multiline:""},{default:t(()=>[(i(!0),u(x,null,C(o.value,(a,r)=>(i(),u("div",{key:r,class:"settings-block--code-line"},f(a[0])+": "+f(a[1]),1))),128))]),_:1})]))}}),O=v({__name:"Settings",async setup(d){let s,o;const g=[{text:"Settings"}],c=A(),[a,r]=([s,o]=w(()=>Promise.all([c.admin.getSettings(),c.admin.getVersion()])),s=await s,o(),s);return q("Settings"),(z,m)=>{const S=l("p-key-value"),b=l("p-theme-toggle"),_=l("p-label"),k=l("p-layout-default");return i(),F(k,{class:"settings"},{header:t(()=>[e(n(j),{crumbs:g},{actions:t(()=>[e(S,{class:"settings__version",label:"Version",value:n(r),alternate:""},null,8,["value"])]),_:1})]),default:t(()=>[e(_,{label:"Theme"},{default:t(()=>[e(b)]),_:1}),e(_,{label:"Color Mode",class:"settings__color-mode"},{default:t(()=>[e(n(P),{selected:n(p),"onUpdate:selected":m[0]||(m[0]=h=>V(p)?p.value=h:null)},null,8,["selected"])]),_:1}),e(_,{label:"Server Settings"},{default:t(()=>[e(T,{class:"settings__code-block","engine-settings":n(a)},null,8,["engine-settings"])]),_:1})]),_:1})}}});export{O as default};
//# sourceMappingURL=Settings-rlAZzjpS.js.map
