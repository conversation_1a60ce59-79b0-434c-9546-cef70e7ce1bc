import{d as V,N as D,ae as M,T as F,i as c,c as v,o as f,j as o,k as t,B as y,l as S,a as h,q as E,m as e,t as L,K as A,da as q,db as I,h as x,cz as W,dc as K,dd as Y,de as i,be as r,H as z,df as H,g as Q,dg as G,dh as X,J as Z,c7 as T,aI as j,bw as ee,di as te,dj as oe,dk as ne,r as se,aw as ae,dl as le,aR as C,dm as ie,dn as re,dp as ue,dq as ce}from"./index-g6FNXcTE.js";import{u as me,c as pe}from"./useCan-CtbALFol.js";import{U as de,u as _e,c as fe,p as ve}from"./api-CQZ_ymP4.js";import"./mapper-3GnQ1V33.js";const ge={class:"flex gap-x-2 items-center"},be={key:0,class:"text-sm italic"},ke="http://prefect.io/slack?utm_source=oss&utm_medium=oss&utm_campaign=oss_popup&utm_term=none&utm_content=none",B="join-the-community-modal",we="https://getform.io/f/eapderva",ye=V({__name:"JoinTheCommunityModal",props:{showModal:{type:Boolean},showModalModifiers:{}},emits:["update:showModal"],setup(a){const s=D(a,"showModal"),u=M(!1),l=M(),{validate:b,state:m}=F(l,[q("Email"),I("Email")]),g=M(!1),d=M("");async function _(){if(await b()){d.value="",g.value=!0;try{await fetch(we,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:l.value}),redirect:"manual"}),s.value=!1,A("Successfully subscribed","success")}catch(k){d.value="An error occurred. Please try again.",console.error(k)}finally{g.value=!1}}}return(k,n)=>{const p=c("p-button"),P=c("p-divider"),U=c("p-text-input"),J=c("p-label"),$=c("p-form"),O=c("p-message"),N=c("p-modal");return f(),v(N,{"show-modal":s.value,"onUpdate:showModal":n[2]||(n[2]=w=>s.value=w),title:"Join the Prefect Community"},{header:o(()=>n[3]||(n[3]=[S("h2",null,"Join the Community",-1)])),default:o(()=>[n[5]||(n[5]=S("p",null," Connect with 25k+ engineers scaling Python with Prefect. Show us your work and be the first to know about new Prefect features. ",-1)),S("div",ge,[t(p,{primary:"",icon:"Slack",to:ke,target:"_blank",onClick:n[0]||(n[0]=w=>u.value=!0)},{default:o(()=>n[4]||(n[4]=[y(" Join us on Slack ")])),_:1,__:[4]}),u.value?(f(),E("span",be," Thanks for joining our community! ")):h("",!0)]),t(P,{class:"-my-3"}),t($,{id:B,onSubmit:_},{default:o(()=>[t(J,{label:"Notify me about Prefect updates",state:e(m),message:e(m).error},{default:o(({id:w})=>[t(U,{id:w,modelValue:l.value,"onUpdate:modelValue":n[1]||(n[1]=R=>l.value=R),placeholder:"<EMAIL>",state:e(m)},null,8,["id","modelValue","state"])]),_:1},8,["state","message"])]),_:1}),d.value?(f(),v(O,{key:0,error:""},{default:o(()=>[y(L(d.value),1)]),_:1})):h("",!0)]),cancel:o(w=>[t(p,{class:"sm:order-first",onClick:w.close},{default:o(()=>n[6]||(n[6]=[y(" Skip ")])),_:2,__:[6]},1032,["onClick"])]),actions:o(()=>[t(p,{primary:"",type:"submit",form:B,loading:g.value},{default:o(()=>n[7]||(n[7]=[y(" Sign up ")])),_:1,__:[7]},8,["loading"])]),_:1},8,["show-modal"])}}}),he={href:"https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss&utm_term=none&utm_content=none",target:"_blank"},Ce=V({__name:"ContextSidebar",setup(a){const s=me(),u=x(()=>s.read.work_pool),{showModal:l,open:b}=W(),{value:m}=K("local","join-the-community-modal-dismissed",!1);function g(d){l.value=d,d||(m.value=!0)}return(d,_)=>{const k=c("p-icon"),n=c("router-link"),p=c("p-button");return f(),v(e(Y),{class:"context-sidebar"},{header:o(()=>[t(n,{to:e(r).root(),class:"context-sidebar__logo-link"},{default:o(()=>[t(k,{icon:"Prefect",class:"context-sidebar__logo-icon"})]),_:1},8,["to"])]),footer:o(()=>[S("a",he,[t(e(i),null,{default:o(()=>[_[1]||(_[1]=S("div",null," Ready to scale? ",-1)),t(p,{primary:"",small:"",class:"context-sidebar__upgade-button"},{default:o(()=>_[0]||(_[0]=[y(" Upgrade ")])),_:1,__:[0]})]),_:1,__:[1]})]),t(e(i),{onClick:e(b)},{default:o(()=>[_[2]||(_[2]=y(" Join the Community ")),t(ye,{"show-modal":e(l)||!e(m),"onUpdate:showModal":g},null,8,["show-modal"])]),_:1,__:[2]},8,["onClick"]),t(e(i),{title:"Settings",to:e(r).settings()},null,8,["to"])]),default:o(()=>[t(e(i),{title:"Dashboard",to:e(r).dashboard()},null,8,["to"]),t(e(i),{title:"Runs",to:e(r).runs()},null,8,["to"]),t(e(i),{title:"Flows",to:e(r).flows()},null,8,["to"]),t(e(i),{title:"Deployments",to:e(r).deployments()},null,8,["to"]),u.value?(f(),v(e(i),{key:0,title:"Work Pools",to:e(r).workPools()},null,8,["to"])):h("",!0),u.value?h("",!0):(f(),v(e(i),{key:1,title:"Work Queues",to:e(r).workQueues()},null,8,["to"])),t(e(i),{title:"Blocks",to:e(r).blocks()},null,8,["to"]),t(e(i),{title:e(z).info.variables,to:e(r).variables()},null,8,["title","to"]),t(e(i),{title:"Automations",to:e(r).automations()},null,8,["to"]),t(e(i),{title:"Event Feed",to:e(r).events()},null,8,["to"]),t(e(i),{title:"Concurrency",to:e(r).concurrencyLimits()},null,8,["to"])]),_:1})}}});async function Me(){const a=await de.get("apiUrl"),s={baseUrl:a};return a.startsWith("/")&&H(),{config:s}}function Se(){const a=Q(_e.getFeatureFlags,[]),s=x(()=>[...G,...a.response??[]]),u=X(s),l=x(()=>a.loading);return{can:u,pending:l}}function Pe(){const a=M(!1);function s(){a.value=!a.value}function u(){a.value=!0}function l(){a.value=!1}return{mobileMenuOpen:a,open:u,close:l,toggle:s}}const xe={class:"app-router-view"},je=V({__name:"AppRouterView",async setup(a){let s,u;const{can:l}=Se(),{config:b}=([s,u]=Z(()=>Me()),s=await s,u(),s),m=fe(b),g=ue();C(pe,l),C(ie,l),C(ve,m),C(re,m),C(ce,g),m.admin.authCheck().then(p=>{p==401?T.currentRoute.value.name!=="login"&&(A("Authentication failed.","error",{timeout:!1}),T.push({name:"login",query:{redirect:T.currentRoute.value.fullPath}})):m.health.isHealthy().then(P=>{P||A(`Can't connect to Server API at ${b.baseUrl}. Check that it's accessible from your machine.`,"error",{timeout:!1})})});const{mobileMenuOpen:d,toggle:_,close:k}=Pe(),n=x(()=>j.lg||d.value);return ee(()=>document.body.classList.toggle("body-scrolling-disabled",n.value&&!j.lg)),(p,P)=>{const U=c("router-link"),J=c("p-button");return f(),E("div",xe,[!e(j).lg&&!p.$route.meta.public?(f(),v(e(oe),{key:0,class:"app-router-view__mobile-menu"},{"upper-links":o(()=>[t(U,{to:e(r).root()},{default:o(()=>[t(e(te),{icon:"Prefect",class:"app-router-view__prefect-icon"})]),_:1},8,["to"])]),"bottom-links":o(()=>[t(J,{small:"",icon:"Bars3Icon",class:"app-router-view__menu-icon",onClick:e(_)},null,8,["onClick"])]),_:1})):h("",!0),n.value&&!p.$route.meta.public?(f(),v(Ce,{key:1,class:"app-router-view__sidebar",onClick:e(k)},null,8,["onClick"])):h("",!0),t(e(le),{class:ae(["app-router-view__view",{"app-router-view__view--public":p.$route.meta.public}])},{default:o(({Component:$})=>[t(ne,{name:"app-router-view-fade",mode:"out-in"},{default:o(()=>[(f(),v(se($)))]),_:2},1024)]),_:1},8,["class"])])}}});export{je as default};
//# sourceMappingURL=AppRouterView-DHgI1Qi_.js.map
