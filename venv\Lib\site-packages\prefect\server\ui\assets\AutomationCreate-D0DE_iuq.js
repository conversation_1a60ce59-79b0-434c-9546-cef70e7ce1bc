import{d as x,V as y,u as h,I as w,J as T,i as V,c as b,o as k,j as s,k as r,m as n,z as B,G as P,H as i,B as j,K as c,L as G}from"./index-g6FNXcTE.js";import{_ as J}from"./AutomationWizard.vue_vue_type_script_setup_true_lang-DFpkKLMX.js";import{u as N}from"./usePageTitle-DEr56mEx.js";import{u as q}from"./usePrefectApi-iH-jzNq9.js";import"./mapper-3GnQ1V33.js";import"./api-CQZ_ymP4.js";const R=x({__name:"AutomationCreate",async setup(v){let e,u;N("Create Automation");const _=q(),m=y(),p=h(),l=[{text:"Automations",to:m.automations()},{text:"Create"}],{getActions:f,getTrigger:d}=w(),g=([e,u]=T(()=>C()),e=await e,u(),e);async function C(){const o={},[t,a]=await Promise.all([d(),f()]);return t&&(o.trigger=t),a&&(o.actions=a),o}async function A(o){try{await _.automations.createAutomation(o),c(i.success.automationCreate),p.push(m.automations())}catch(t){console.error(t);const a=G(t,i.error.automationCreate);c(a,"error",{timeout:!1})}}return(o,t)=>{const a=V("p-layout-default");return k(),b(a,{class:"automation-create"},{header:s(()=>[r(n(B),{crumbs:l},{actions:s(()=>[r(n(P),{to:n(i).docs.automations},{default:s(()=>t[0]||(t[0]=[j(" Documentation ")])),_:1,__:[0]},8,["to"])]),_:1})]),default:s(()=>[r(J,{automation:n(g),onSubmit:A},null,8,["automation"])]),_:1})}}});export{R as default};
//# sourceMappingURL=AutomationCreate-D0DE_iuq.js.map
