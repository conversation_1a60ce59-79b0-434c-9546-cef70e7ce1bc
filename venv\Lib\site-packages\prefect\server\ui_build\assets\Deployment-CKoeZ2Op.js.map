{"version": 3, "file": "Deployment-CKoeZ2Op.js", "sources": ["../../src/pages/Deployment.vue"], "sourcesContent": ["<template>\n  <p-layout-well v-if=\"deployment\" class=\"deployment\">\n    <template #header>\n      <PageHeadingDeployment\n        :deployment=\"deployment\"\n        @update=\"deploymentSubscription.refresh\"\n        @delete=\"routeToDeployments\"\n      />\n    </template>\n\n\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #description>\n        <p-content secondary>\n          <DeploymentDeprecatedMessage v-if=\"deployment.deprecated\" />\n          <template v-else-if=\"deployment.description\">\n            <DeploymentDescription :description=\"deployment.description\" />\n          </template>\n          <template v-else>\n            <DeploymentDescriptionEmptyState :deployment=\"deployment\" />\n          </template>\n        </p-content>\n      </template>\n\n      <template #parameters>\n        <ParametersTable :deployment=\"deployment\" />\n      </template>\n\n      <template #configuration>\n        <DeploymentConfiguration :deployment=\"deployment\" />\n      </template>\n\n      <template #details>\n        <DeploymentDetails :deployment=\"deployment\" @update=\"deploymentSubscription.refresh\" />\n      </template>\n\n      <template #runs>\n        <template v-if=\"nextRun\">\n          <p-heading heading=\"6\" class=\"deployment__next-run\">\n            Next Run\n          </p-heading>\n          <FlowRunListItem :flow-run=\"nextRun\" />\n          <p-divider />\n        </template>\n        <FlowRunFilteredList :filter=\"flowRunsFilter\" selectable prefix=\"runs\" />\n      </template>\n\n      <template #upcoming>\n        <FlowRunFilteredList :filter=\"upcomingFlowRunsFilter\" selectable prefix=\"upcoming\" />\n      </template>\n    </p-tabs>\n\n    <template #well>\n      <DeploymentDetails :deployment=\"deployment\" alternate @update=\"deploymentSubscription.refresh\" />\n    </template>\n  </p-layout-well>\n</template>\n\n<script lang=\"ts\" setup>\n  import { media } from '@prefecthq/prefect-design'\n  import { DeploymentDescription, FlowRunListItem, DeploymentDetails, DeploymentDescriptionEmptyState, PageHeadingDeployment, ParametersTable, DeploymentDeprecatedMessage, useTabs, useWorkspaceApi, useFlowRunsFilter, prefectStateNames, DeploymentConfiguration, useNextFlowRun, FlowRunFilteredList } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam, useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n\n\n  const deploymentId = useRouteParam('deploymentId')\n  const deploymentIds = computed(() => [deploymentId.value])\n  const router = useRouter()\n  const api = useWorkspaceApi()\n\n  const subscriptionOptions = {\n    interval: 300000,\n  }\n\n  const deploymentSubscription = useSubscription(api.deployments.getDeployment, [deploymentId.value], subscriptionOptions)\n  const deployment = computed(() => deploymentSubscription.response)\n\n  const computedTabs = computed(() => [\n    { label: 'Details', hidden: media.xl },\n    { label: 'Runs' },\n    { label: 'Upcoming' },\n    { label: 'Parameters', hidden: deployment.value?.deprecated },\n    { label: 'Configuration', hidden: deployment.value?.deprecated },\n    { label: 'Description' },\n  ])\n  const tab = useRouteQueryParam('tab', 'Details')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  function routeToDeployments(): void {\n    router.push(routes.deployments())\n  }\n\n  const { filter: flowRunsFilter } = useFlowRunsFilter({\n    deployments: {\n      id: deploymentIds,\n    },\n    flowRuns: {\n      state: {\n        name: prefectStateNames.filter(stateName => stateName !== 'Scheduled'),\n      },\n    },\n  })\n\n  const { filter: upcomingFlowRunsFilter } = useFlowRunsFilter({\n    sort: 'START_TIME_ASC',\n    deployments: {\n      id: deploymentIds,\n    },\n    flowRuns: {\n      state: {\n        name: ['Scheduled'],\n      },\n    },\n  })\n\n  const { flowRun: nextRun } = useNextFlowRun(() => ({\n    deployments: {\n      id: deploymentIds.value,\n    },\n  }))\n\n\n  const title = computed(() => {\n    if (!deployment.value) {\n      return 'Deployment'\n    }\n    return `Deployment: ${deployment.value.name}`\n  })\n  usePageTitle(title)\n</script>\n\n<style>\n.deployment__next-run { @apply\n  mb-2\n}\n</style>"], "names": ["deploymentId", "useRouteParam", "deploymentIds", "computed", "router", "useRouter", "api", "useWorkspaceApi", "subscriptionOptions", "deploymentSubscription", "useSubscription", "deployment", "computedTabs", "media", "_a", "_b", "tab", "useRouteQueryParam", "tabs", "useTabs", "routeToDeployments", "routes", "flowRunsFilter", "useFlowRunsFilter", "prefectStateNames", "stateName", "upcomingFlowRuns<PERSON><PERSON>er", "nextRun", "useNextFlowRun", "title", "usePageTitle", "_createBlock", "_component_p_layout_well", "_createVNode", "_unref", "PageHeadingDeployment", "DeploymentDetails", "_component_p_tabs", "$event", "_component_p_content", "DeploymentDeprecatedMessage", "DeploymentDescription", "DeploymentDescriptionEmptyState", "ParametersTable", "DeploymentConfiguration", "_createElementBlock", "_Fragment", "_component_p_heading", "_cache", "FlowRunListItem", "_component_p_divider", "FlowRunFilteredList"], "mappings": "gXAoEQ,MAAAA,EAAeC,EAAc,cAAc,EAC3CC,EAAgBC,EAAS,IAAM,CAACH,EAAa,KAAK,CAAC,EACnDI,EAASC,EAAU,EACnBC,EAAMC,EAAgB,EAEtBC,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAAyBC,EAAgBJ,EAAI,YAAY,cAAe,CAACN,EAAa,KAAK,EAAGQ,CAAmB,EACjHG,EAAaR,EAAS,IAAMM,EAAuB,QAAQ,EAE3DG,EAAeT,EAAS,IAAM,SAAA,OAClC,CAAE,MAAO,UAAW,OAAQU,EAAM,EAAG,EACrC,CAAE,MAAO,MAAO,EAChB,CAAE,MAAO,UAAW,EACpB,CAAE,MAAO,aAAc,QAAQC,EAAAH,EAAW,QAAX,YAAAG,EAAkB,UAAW,EAC5D,CAAE,MAAO,gBAAiB,QAAQC,EAAAJ,EAAW,QAAX,YAAAI,EAAkB,UAAW,EAC/D,CAAE,MAAO,aAAc,CAAA,EACxB,EACKC,EAAMC,EAAmB,MAAO,SAAS,EACzC,CAAE,KAAAC,CAAS,EAAAC,EAAQP,EAAcI,CAAG,EAE1C,SAASI,GAA2B,CAC3BhB,EAAA,KAAKiB,GAAO,aAAa,CAAA,CAGlC,KAAM,CAAE,OAAQC,CAAe,EAAIC,EAAkB,CACnD,YAAa,CACX,GAAIrB,CACN,EACA,SAAU,CACR,MAAO,CACL,KAAMsB,GAAkB,OAAOC,GAAaA,IAAc,WAAW,CAAA,CACvE,CACF,CACD,EAEK,CAAE,OAAQC,CAAuB,EAAIH,EAAkB,CAC3D,KAAM,iBACN,YAAa,CACX,GAAIrB,CACN,EACA,SAAU,CACR,MAAO,CACL,KAAM,CAAC,WAAW,CAAA,CACpB,CACF,CACD,EAEK,CAAE,QAASyB,CAAQ,EAAIC,EAAe,KAAO,CACjD,YAAa,CACX,GAAI1B,EAAc,KAAA,CACpB,EACA,EAGI2B,EAAQ1B,EAAS,IAChBQ,EAAW,MAGT,eAAeA,EAAW,MAAM,IAAI,GAFlC,YAGV,EACD,OAAAmB,GAAaD,CAAK,6GAlIGlB,EAAU,WAA/BoB,EAsDgBC,EAAA,OAtDiB,MAAM,YAAA,GAC1B,SACT,IAIE,CAJFC,EAIEC,EAAAC,CAAA,EAAA,CAHC,WAAYxB,EAAU,MACtB,SAAQuB,EAAsBzB,CAAA,EAAC,QAC/B,SAAQW,uCA8CF,OACT,IAAiG,CAAjGa,EAAiGC,EAAAE,CAAA,EAAA,CAA7E,WAAYzB,EAAU,MAAE,UAAA,GAAW,SAAQuB,EAAsBzB,CAAA,EAAC,uDA1CxF,IAuCS,CAvCTwB,EAuCSI,EAAA,CAvCO,SAAUH,EAAGlB,CAAA,0CAAHA,EAAG,MAAAsB,EAAA,MAAG,KAAMJ,EAAIhB,CAAA,CAAA,GAC7B,cACT,IAQY,CARZe,EAQYM,EAAA,CARD,UAAA,IAAS,WAClB,IAA4D,CAAzB5B,EAAA,MAAW,gBAA9CoB,EAA4DG,EAAAM,CAAA,EAAA,CAAA,IAAA,EAAA,GACvC7B,EAAA,MAAW,iBAC9BoB,EAA+DG,EAAAO,CAAA,EAAA,OAAvC,YAAa9B,EAAU,MAAC,WAAA,gCAGhDoB,EAA4DG,EAAAQ,CAAA,EAAA,OAA1B,WAAY/B,EAAU,yCAKnD,aACT,IAA4C,CAA5CsB,EAA4CC,EAAAS,CAAA,EAAA,CAA1B,WAAYhC,EAAU,OAAA,KAAA,EAAA,CAAA,YAAA,CAAA,CAAA,GAG/B,gBACT,IAAoD,CAApDsB,EAAoDC,EAAAU,CAAA,EAAA,CAA1B,WAAYjC,EAAU,OAAA,KAAA,EAAA,CAAA,YAAA,CAAA,CAAA,GAGvC,UACT,IAAuF,CAAvFsB,EAAuFC,EAAAE,CAAA,EAAA,CAAnE,WAAYzB,EAAU,MAAG,SAAQuB,EAAsBzB,CAAA,EAAC,6CAGnE,OACT,IAMW,CANKyB,EAAOP,CAAA,OAAvBkB,EAMWC,EAAA,CAAA,IAAA,GAAA,CALTb,EAEYc,EAAA,CAFD,QAAQ,IAAI,MAAM,sBAAA,aAAuB,IAEpDC,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAFoD,YAEpD,CAAA,iBACAf,EAAuCC,EAAAe,CAAA,EAAA,CAArB,WAAUf,EAAOP,CAAA,CAAA,EAAA,KAAA,EAAA,CAAA,UAAA,CAAA,EACnCM,EAAaiB,CAAA,iBAEfjB,EAAyEC,EAAAiB,CAAA,EAAA,CAAnD,OAAQjB,EAAcZ,CAAA,EAAE,WAAA,GAAW,OAAO,MAAA,uBAGvD,WACT,IAAqF,CAArFW,EAAqFC,EAAAiB,CAAA,EAAA,CAA/D,OAAQjB,EAAsBR,CAAA,EAAE,WAAA,GAAW,OAAO,UAAA"}