import{d as T,f as F,u as x,al as P,W as w,h as o,bF as m,i as A,c as r,a as u,o as n,j as p,m as k,cn as D,co as N,Z as V,cp as W,k as Z,cq as j,K as f,cr as q,be as H}from"./index-g6FNXcTE.js";import{u as I}from"./usePageTitle-DEr56mEx.js";const R=T({__name:"BlocksCatalogCreate",setup(J){const s=F(),t=x(),i=P("redirect"),b=w("blockTypeSlug"),y=o(()=>b.value?[b.value]:null),d=m(s.blockTypes.getBlockTypeBySlug,y),e=o(()=>d.response),v=o(()=>e.value?[e.value.id]:null),h=m(s.blockSchemas.getBlockSchemaForBlockType,v),l=o(()=>h.response);function _(a){s.blockDocuments.createBlockDocument(a).then(({id:c})=>g(c)).catch(c=>{f("Failed to create block","error"),console.error(c)})}function S(){t.back()}function g(a){if(f("Block created successfully","success"),i.value){const c=t.resolve(q(i.value));t.push(c);return}t.push(H.block(a))}const B=o(()=>e.value?`Create ${e.value.name} Block`:"Create Block");return I(B),(a,c)=>{const C=A("p-layout-default");return e.value?(n(),r(C,{key:0,class:"blocks-catalog-create"},{header:p(()=>[Z(k(j),{"block-type":e.value},null,8,["block-type"])]),default:p(()=>[e.value?(n(),r(k(D),{key:0,"block-type":e.value},{default:p(()=>[l.value?(n(),r(k(N),V({key:l.value.id,"block-schema":l.value},W({submit:_,cancel:S})),null,16,["block-schema"])):u("",!0)]),_:1},8,["block-type"])):u("",!0)]),_:1})):u("",!0)}}});export{R as default};
//# sourceMappingURL=BlocksCatalogCreate-DIcNxSkp.js.map
