from prefect import task, get_run_logger
from prefect.tasks import NO_CACHE
import requests
import asyncio
import aiohttp

class ShopifyTask():

    async def get_deployment_url_by_name(self, session, api_url, name, headers):
        async with session.get(f"{api_url}/deployments/name/{name}", headers=headers) as resp:
            data = await resp.json()
            trigger_api_url = f"{api_url}/deployments/{data['id']}/create_flow_run"
            return trigger_api_url

    @task(cache_policy=NO_CACHE)
    async def get_shopify_data(self, shop_name, shop_access_token, start_date, end_date):
        logger = get_run_logger()
        try:
            headers = {
                "X-Shopify-Access-Token": shop_access_token,
                "Content-Type": "application/json"
            }
            get_url = f"https://{shop_name}.shopify.com/admin/api/unstable/orders.json?created_at_min={start_date}&created_at_max={end_date}"
            get_response = requests.get(get_url, headers=headers)
            return get_response.json()
        except Exception as e:
            logger.error(f"Error getting shopify data: {e}")
            raise e

    @task(cache_policy=NO_CACHE)
    async def load_shopify_data(self, shopify_data):
        await asyncio.sleep(10)

    @task(cache_policy=NO_CACHE)
    async def invoke_load_shopify_data(self, shopify_data):
        logger = get_run_logger()
        # schedule the child flow to run asynchronously
        headers = {
            "Content-Type": "application/json"
        }
        api_url = "http://localhost:4200/api"
        async with aiohttp.ClientSession() as session:
            trigger_api_url = await self.get_deployment_url_by_name(session, api_url, "load_shopify_data/load_shopify_data", headers)
            async with session.post(trigger_api_url,
                                    headers=headers,
                                    json={
                                        "parameters": {
                                            "shopify_data": shopify_data
                                        }
                                    },
            ) as resp:
                logger.info(await resp.text())
                