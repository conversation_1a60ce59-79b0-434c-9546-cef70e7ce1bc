from prefect import task
import requests

class ShopifyTask():

    @task
    async def get_shopify_data(self, shop_name, shop_access_token, start_date, end_date):
        headers = {
            "X-Shopify-Access-Token": shop_access_token,
            "Content-Type": "application/json"
        }
        get_url = f"https://{shop_name}.shopify.com/admin/api/unstable/orders.json?created_at_min={start_date}&created_at_max={end_date}"
        get_response = requests.get(get_url, headers=headers)
        return get_response.json()