var i=Object.defineProperty;var o=(t,e,n)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var a=(t,e,n)=>o(t,typeof e!="symbol"?e+"":e,n);import{cP as s,cQ as r,cR as g,cS as m}from"./index-g6FNXcTE.js";class p extends s{constructor(n){super(n);a(this,"trigger");a(this,"actions");this.trigger=n.trigger,this.actions=n.actions}}function h(t){return!!t.trigger}const u=function(t){return new p({id:t.id,name:t.name,description:t.description,enabled:t.enabled,trigger:this.map("AutomationTriggerResponse",t.trigger,"AutomationTrigger"),actions:this.map("AutomationActionResponse",t.actions,"AutomationAction")})},c=function(t){return{name:t.name,description:t.description,enabled:t.enabled,trigger:this.map("AutomationTrigger",t.trigger,"AutomationTriggerRequest"),actions:this.map("AutomationAction",t.actions,"AutomationActionRequest")}},A=function(t){return{token:t.token,expiration:this.map("string",t.expiration,"Date"),issued:new Date}},l=t=>{switch(t){case"workers":return"access:workers";case"artifacts":return"access:artifacts";default:return null}},f=function(t){return{apiUrl:t.api_url,csrfEnabled:t.csrf_enabled,auth:t.auth,flags:this.map("FlagResponse",t.flags,"FeatureFlag").filter(r)}},R={...g,FlagResponse:{FeatureFlag:l},SettingsResponse:{Settings:f},CsrfTokenResponse:{CsrfToken:A},AutomationResponse:{Automation:u},AutomationCreate:{AutomationCreateRequest:c}},F=new m(R);export{p as A,h as i,F as m};
//# sourceMappingURL=mapper-3GnQ1V33.js.map
