import{d,f as i,g as y,h as o,i as _,c as a,o as t,j as n,q as f,a as g,F as k,m as s,bV as h,bS as D,k as b,bW as v}from"./index-g6FNXcTE.js";import{u as x}from"./usePageTitle-DEr56mEx.js";const N=d({__name:"Deployments",setup(C){const l=i(),c={interval:3e4},e=y(l.deployments.getDeployments,[{}],c),p=o(()=>e.response??[]),r=o(()=>e.executed&&p.value.length===0),m=o(()=>e.executed);return x("Deployments"),(B,V)=>{const u=_("p-layout-default");return t(),a(u,{class:"deployments"},{header:n(()=>[b(s(v))]),default:n(()=>[m.value?(t(),f(k,{key:0},[r.value?(t(),a(s(h),{key:0})):(t(),a(s(D),{key:1,onDelete:s(e).refresh},null,8,["onDelete"]))],64)):g("",!0)]),_:1})}}});export{N as default};
//# sourceMappingURL=Deployments-DZ_KO9eC.js.map
