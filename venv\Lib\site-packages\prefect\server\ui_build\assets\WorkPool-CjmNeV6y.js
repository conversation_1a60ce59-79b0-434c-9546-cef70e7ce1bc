import{d as N,f as B,W as R,g as U,h as s,aI as F,al as I,bt as T,bu as V,bR as $,i as m,c as i,a as k,o as d,j as a,k as t,m as e,a6 as L,bS as S,cT as Z,cU as j,bz as q,cV as w,cW as z,cX as A}from"./index-g6FNXcTE.js";import{u as G}from"./usePageTitle-DEr56mEx.js";const X=N({__name:"WorkPool",setup(H){const b=B(),n=R("workPoolName"),f={interval:3e5},u=U(b.workPools.getWorkPoolByName,[n.value],f),o=s(()=>u.response),c=s(()=>{var l;return((l=o.value)==null?void 0:l.type)==="prefect-agent"}),v=s(()=>[{label:"Details",hidden:F.xl},{label:"Runs"},{label:"Work Queues"},{label:"Workers",hidden:c.value},{label:"Deployments"}]),r=I("tab","Details"),{tabs:_}=T(v,r),P=s(()=>{var l;return((l=o.value)==null?void 0:l.status)!=="ready"}),y=s(()=>{var l;return`prefect ${c.value?"agent":"worker"} start --pool "${(l=o.value)==null?void 0:l.name}"`}),{filter:W}=V({workPools:{name:[n.value]}}),{filter:g}=$({workPools:{name:[n.value]}}),h=s(()=>o.value?`Work Pool: ${o.value.name}`:"Work Pool");return G(h),(l,p)=>{const C=m("p-tabs"),x=m("p-layout-well");return o.value?(d(),i(x,{key:0,class:"work-pool"},{header:a(()=>[t(e(z),{"work-pool":o.value,onUpdate:e(u).refresh},null,8,["work-pool","onUpdate"]),P.value?(d(),i(e(A),{key:0,class:"work-pool__code-banner",command:y.value,title:"Your work pool is almost ready!",subtitle:"Run this command to start."},null,8,["command"])):k("",!0)]),well:a(()=>[t(e(w),{alternate:"","work-pool":o.value},null,8,["work-pool"])]),default:a(()=>[t(C,{selected:e(r),"onUpdate:selected":p[0]||(p[0]=D=>L(r)?r.value=D:null),tabs:e(_)},{details:a(()=>[t(e(w),{"work-pool":o.value},null,8,["work-pool"])]),runs:a(()=>[t(e(q),{filter:e(W),prefix:"runs"},null,8,["filter"])]),"work-queues":a(()=>[t(e(j),{"work-pool-name":e(n)},null,8,["work-pool-name"])]),workers:a(()=>[t(e(Z),{"work-pool-name":e(n)},null,8,["work-pool-name"])]),deployments:a(()=>[t(e(S),{filter:e(g)},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})):k("",!0)}}});export{X as default};
//# sourceMappingURL=WorkPool-CjmNeV6y.js.map
