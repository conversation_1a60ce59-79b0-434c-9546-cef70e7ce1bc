{"version": 3, "file": "Events-8_WC1YwF.js", "sources": ["../../node_modules/lodash.debounce/index.js", "../../src/pages/Events.vue"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        result = wait - timeSinceLastCall;\n\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = debounce;\n", "<template>\n  <p-layout-default v-if=\"startDate && endDate\" class=\"events\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\" />\n    </template>\n\n    <div class=\"events__content\">\n      <div class=\"events__filters\">\n        <p-label label=\"Resource\">\n          <template #default=\"{ id }\">\n            <EventResourceCombobox :id v-model:selected=\"resources\" multiple />\n          </template>\n        </p-label>\n\n        <p-label label=\"Events\">\n          <template #default=\"{ id }\">\n            <EventsCombobox :id v-model:selected=\"eventsQueryParam\" multiple />\n          </template>\n        </p-label>\n      </div>\n\n      <div ref=\"chart\" class=\"events__chart p-background\" :class=\"classes.chart\">\n        <WorkspaceEventsLineChart v-model:start-date=\"startDate\" v-model:end-date=\"endDate\" :filter :zoom-options />\n        <div class=\"events__controls\">\n          <DateRangeSelect v-model=\"dateRange.range\" :max=\"now\" />\n        </div>\n      </div>\n\n      <template v-if=\"loading && empty\">\n        <p-empty-results>\n          <template #message>\n            Loading events\n          </template>\n        </p-empty-results>\n      </template>\n\n      <template v-else>\n        <template v-if=\"empty\">\n          <p-empty-results>\n            <template #message>\n              No events\n            </template>\n          </p-empty-results>\n        </template>\n\n        <WorkspaceEventsTimeline class=\"events__timeline\" :events :start-date :end-date />\n\n        <p-pager\n          v-if=\"events.length\"\n          v-model:page=\"page\"\n          v-model:pages=\"pages\"\n          class=\"events__pager\"\n        />\n      </template>\n    </div>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { Crumb, useOffsetStickyRootMargin } from '@prefecthq/prefect-design'\n  import {\n    Getter,\n    PageHeading,\n    separate,\n    DateRangeSelect,\n    useDateRangeSelectValueFromRoute,\n    secondsInDay,\n    useWorkspaceEventsSubscription,\n    EventsCombobox,\n    EventResourceCombobox,\n    WorkspaceEventsFilter,\n    WorkspaceEventsTimeline,\n    WorkspaceEventsLineChart,\n    secondsInHour,\n    secondsInWeek\n  } from '@prefecthq/prefect-ui-library'\n  import { ChartZoomOptions, useChartSelection, useChartZoom } from '@prefecthq/vue-charts'\n  import { UsePositionStickyObserverOptions, useNow, usePositionStickyObserver, useRouteQueryParam } from '@prefecthq/vue-compositions'\n  import debounce from 'lodash.debounce'\n  import { computed, ref, toRef, watch } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { mapper } from '@/services/mapper'\n\n  const crumbs = computed<Crumb[]>(() => [{ text: 'Event Feed' }])\n  const chart = ref()\n  const interval = 60_000\n  const { now } = useNow({ interval })\n\n  usePageTitle('Event Feed')\n\n  const resources = useRouteQueryParam('resource', [])\n  const eventsQueryParam = useRouteQueryParam('events', [])\n\n  const dateRange = useDateRangeSelectValueFromRoute({\n    type: 'span',\n    seconds: -secondsInDay,\n  })\n\n  const { startDate, endDate } = useChartZoom()\n  const { selectionStart, selectionEnd } = useChartSelection()\n\n  // minus one second from each to get clean dates like 10:00AM - 10:59AM\n  const zoomOptions: ChartZoomOptions = {\n    minRangeInSeconds: secondsInHour - 1,\n    maxRangeInSeconds: secondsInWeek - 1,\n  }\n\n  const filter = toRef(() => {\n    const { startDate, endDate } = mapper.map('DateRangeSelectValue', dateRange.range, 'DateRange')!\n    const [prefixed, name] = separate(eventsQueryParam.value, event => event.endsWith('*'))\n    const prefix = prefixed.map(prefix => prefix.replace('.*', '.'))\n\n    // Exclude some events by default unless they are explicitly requested\n    const excludedByDefault = ['prefect.log.write']\n    const excludeName = excludedByDefault.filter(excluded => !name.includes(excluded))\n\n    const filter: WorkspaceEventsFilter = {\n      occurred: {\n        since: selectionStart.value ?? startDate,\n        until: selectionEnd.value ?? endDate,\n      },\n      anyResource: {\n        idPrefix: resources.value,\n      },\n      event: {\n        prefix,\n        name,\n        excludeName,\n      },\n    }\n\n    return filter\n  })\n\n  const { page, pages, loading, events, empty } = useWorkspaceEventsSubscription(filter, { interval })\n\n  const { margin } = useOffsetStickyRootMargin()\n  const stickyObserverOptions: Getter<UsePositionStickyObserverOptions> = () => ({\n    rootMargin: margin.value,\n  })\n  const { stuck } = usePositionStickyObserver(chart, stickyObserverOptions)\n  const classes = computed(() => ({\n    chart: {\n      'events__chart--stuck': stuck.value,\n    },\n  }))\n\n  let updatedDateRange = false\n\n  const updateDateRangeSelectValue = debounce(() => {\n    if (!startDate.value || !endDate.value) {\n      return\n    }\n\n    if (updatedDateRange) {\n      updatedDateRange = false\n      return\n    }\n\n    dateRange.range = {\n      type: 'range',\n      startDate: startDate.value,\n      endDate: endDate.value,\n    }\n  }, 1000)\n\n  watch([startDate, endDate], () => updateDateRangeSelectValue())\n\n  function updateDateRangeValue(): void {\n    if (!dateRange.range) {\n      return\n    }\n\n    const { startDate: newStart, endDate: newEnd } = mapper.map('DateRangeSelectValue', dateRange.range, 'DateRange')\n\n    if (startDate.value?.getTime() !== newStart.getTime()) {\n      updatedDateRange = true\n      startDate.value = newStart\n    }\n\n    if (endDate.value?.getTime() !== newEnd.getTime()) {\n      updatedDateRange = true\n      endDate.value = newEnd\n    }\n  }\n\n  watch(() => dateRange.range, () => updateDateRangeValue())\n\n  updateDateRangeValue()\n</script>\n\n<style>\n.events__content { @apply\n  grid\n  gap-1\n  grid-cols-1\n}\n\n.events__filters { @apply\n  flex\n  flex-col\n  gap-4\n  sm:flex-row\n  mb-7\n}\n\n.events__chart { @apply\n  sticky\n  top-0\n  z-10\n  pb-3\n  grid\n  grid-cols-1\n  gap-3\n  rounded-t-default\n}\n\n.events__chart--stuck { @apply\n  bg-floating-sticky\n  backdrop-blur-sm\n  shadow-md\n  rounded-t-none\n  rounded-b-default\n}\n\n.events__controls { @apply\n  flex\n  justify-center\n  p-3\n}\n\n.events__timeline { @apply\n  mt-7\n}\n\n.events__pager { @apply\n  mt-4\n}\n</style>\n"], "names": ["FUNC_ERROR_TEXT", "NAN", "symbolTag", "reTrim", "reIsBadHex", "reIsBinary", "reIsOctal", "freeParseInt", "freeGlobal", "global", "freeSelf", "root", "objectProto", "objectToString", "nativeMax", "nativeMin", "now", "debounce", "func", "wait", "options", "lastArgs", "lastThis", "max<PERSON><PERSON>", "result", "timerId", "lastCallTime", "lastInvokeTime", "leading", "maxing", "trailing", "toNumber", "isObject", "invokeFunc", "time", "args", "thisArg", "leading<PERSON>dge", "timerExpired", "remainingWait", "timeSinceLastCall", "timeSinceLastInvoke", "shouldInvoke", "trailingEdge", "cancel", "flush", "debounced", "isInvoking", "value", "type", "isObjectLike", "isSymbol", "other", "isBinary", "lodash_debounce", "interval", "crumbs", "computed", "chart", "ref", "useNow", "usePageTitle", "resources", "useRouteQueryParam", "eventsQueryParam", "date<PERSON><PERSON><PERSON>", "useDateRangeSelectValueFromRoute", "startDate", "endDate", "useChartZoom", "selectionStart", "selectionEnd", "useChartSelection", "zoomOptions", "secondsInHour", "secondsInWeek", "filter", "toRef", "mapper", "prefixed", "name", "separate", "event", "prefix", "excludeName", "excluded", "page", "pages", "loading", "events", "empty", "useWorkspaceEventsSubscription", "margin", "useOffsetStickyRootMargin", "stickyObserverOptions", "stuck", "usePositionStickyObserver", "classes", "updatedDateRange", "updateDateRangeSelectValue", "watch", "updateDateRangeValue", "newStart", "newEnd", "_a", "_b", "_unref", "_createBlock", "_component_p_layout_default", "_createVNode", "PageHeading", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_component_p_label", "_withCtx", "id", "EventResourceCombobox", "$event", "EventsCombobox", "_normalizeClass", "WorkspaceEventsLineChart", "_hoisted_3", "DateRangeSelect", "_cache", "_component_p_empty_results", "_createElementBlock", "_Fragment", "WorkspaceEventsTimeline", "_component_p_pager"], "mappings": "ogBAUA,IAAIA,EAAkB,sBAGlBC,EAAM,IAGNC,EAAY,kBAGZC,EAAS,aAGTC,EAAa,qBAGbC,EAAa,aAGbC,EAAY,cAGZC,EAAe,SAGfC,EAAa,OAAOC,GAAU,UAAYA,GAAUA,EAAO,SAAW,QAAUA,EAGhFC,EAAW,OAAO,MAAQ,UAAY,MAAQ,KAAK,SAAW,QAAU,KAGxEC,EAAOH,GAAcE,GAAY,SAAS,aAAa,EAAG,EAG1DE,EAAc,OAAO,UAOrBC,EAAiBD,EAAY,SAG7BE,EAAY,KAAK,IACjBC,EAAY,KAAK,IAkBjBC,EAAM,UAAW,CACnB,OAAOL,EAAK,KAAK,IAAK,CACvB,EAwDD,SAASM,EAASC,EAAMC,EAAMC,EAAS,CACrC,IAAIC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAiB,EACjBC,EAAU,GACVC,EAAS,GACTC,EAAW,GAEf,GAAI,OAAOZ,GAAQ,WACjB,MAAM,IAAI,UAAUlB,CAAe,EAErCmB,EAAOY,EAASZ,CAAI,GAAK,EACrBa,EAASZ,CAAO,IAClBQ,EAAU,CAAC,CAACR,EAAQ,QACpBS,EAAS,YAAaT,EACtBG,EAAUM,EAASf,EAAUiB,EAASX,EAAQ,OAAO,GAAK,EAAGD,CAAI,EAAII,EACrEO,EAAW,aAAcV,EAAU,CAAC,CAACA,EAAQ,SAAWU,GAG1D,SAASG,EAAWC,EAAM,CACxB,IAAIC,EAAOd,EACPe,EAAUd,EAEd,OAAAD,EAAWC,EAAW,OACtBK,EAAiBO,EACjBV,EAASN,EAAK,MAAMkB,EAASD,CAAI,EAC1BX,CACX,CAEE,SAASa,GAAYH,EAAM,CAEzB,OAAAP,EAAiBO,EAEjBT,EAAU,WAAWa,EAAcnB,CAAI,EAEhCS,EAAUK,EAAWC,CAAI,EAAIV,CACxC,CAEE,SAASe,GAAcL,EAAM,CAC3B,IAAIM,EAAoBN,EAAOR,EAC3Be,EAAsBP,EAAOP,EAC7BH,EAASL,EAAOqB,EAEpB,OAAOX,EAASd,EAAUS,EAAQD,EAAUkB,CAAmB,EAAIjB,CACvE,CAEE,SAASkB,EAAaR,EAAM,CAC1B,IAAIM,EAAoBN,EAAOR,EAC3Be,EAAsBP,EAAOP,EAKjC,OAAQD,IAAiB,QAAcc,GAAqBrB,GACzDqB,EAAoB,GAAOX,GAAUY,GAAuBlB,CACnE,CAEE,SAASe,GAAe,CACtB,IAAIJ,EAAOlB,EAAK,EAChB,GAAI0B,EAAaR,CAAI,EACnB,OAAOS,EAAaT,CAAI,EAG1BT,EAAU,WAAWa,EAAcC,GAAcL,CAAI,CAAC,CAC1D,CAEE,SAASS,EAAaT,EAAM,CAK1B,OAJAT,EAAU,OAINK,GAAYT,EACPY,EAAWC,CAAI,GAExBb,EAAWC,EAAW,OACfE,EACX,CAEE,SAASoB,IAAS,CACZnB,IAAY,QACd,aAAaA,CAAO,EAEtBE,EAAiB,EACjBN,EAAWK,EAAeJ,EAAWG,EAAU,MACnD,CAEE,SAASoB,IAAQ,CACf,OAAOpB,IAAY,OAAYD,EAASmB,EAAa3B,EAAG,CAAE,CAC9D,CAEE,SAAS8B,GAAY,CACnB,IAAIZ,EAAOlB,EAAK,EACZ+B,EAAaL,EAAaR,CAAI,EAMlC,GAJAb,EAAW,UACXC,EAAW,KACXI,EAAeQ,EAEXa,EAAY,CACd,GAAItB,IAAY,OACd,OAAOY,GAAYX,CAAY,EAEjC,GAAIG,EAEF,OAAAJ,EAAU,WAAWa,EAAcnB,CAAI,EAChCc,EAAWP,CAAY,CAEtC,CACI,OAAID,IAAY,SACdA,EAAU,WAAWa,EAAcnB,CAAI,GAElCK,CACX,CACE,OAAAsB,EAAU,OAASF,GACnBE,EAAU,MAAQD,GACXC,CACT,CA2BA,SAASd,EAASgB,EAAO,CACvB,IAAIC,EAAO,OAAOD,EAClB,MAAO,CAAC,CAACA,IAAUC,GAAQ,UAAYA,GAAQ,WACjD,CA0BA,SAASC,EAAaF,EAAO,CAC3B,MAAO,CAAC,CAACA,GAAS,OAAOA,GAAS,QACpC,CAmBA,SAASG,EAASH,EAAO,CACvB,OAAO,OAAOA,GAAS,UACpBE,EAAaF,CAAK,GAAKnC,EAAe,KAAKmC,CAAK,GAAK9C,CAC1D,CAyBA,SAAS6B,EAASiB,EAAO,CACvB,GAAI,OAAOA,GAAS,SAClB,OAAOA,EAET,GAAIG,EAASH,CAAK,EAChB,OAAO/C,EAET,GAAI+B,EAASgB,CAAK,EAAG,CACnB,IAAII,EAAQ,OAAOJ,EAAM,SAAW,WAAaA,EAAM,QAAO,EAAKA,EACnEA,EAAQhB,EAASoB,CAAK,EAAKA,EAAQ,GAAMA,CAC7C,CACE,GAAI,OAAOJ,GAAS,SAClB,OAAOA,IAAU,EAAIA,EAAQ,CAACA,EAEhCA,EAAQA,EAAM,QAAQ7C,EAAQ,EAAE,EAChC,IAAIkD,EAAWhD,EAAW,KAAK2C,CAAK,EACpC,OAAQK,GAAY/C,EAAU,KAAK0C,CAAK,EACpCzC,EAAayC,EAAM,MAAM,CAAC,EAAGK,EAAW,EAAI,CAAC,EAC5CjD,EAAW,KAAK4C,CAAK,EAAI/C,EAAM,CAAC+C,CACvC,CAEA,OAAAM,EAAiBrC,wHCnSTsC,GAAW,oCAFX,MAAAC,EAASC,GAAkB,IAAM,CAAC,CAAE,KAAM,YAAA,CAAc,CAAC,EACzDC,EAAQC,GAAI,EAEZ,CAAE,IAAA3C,CAAI,EAAI4C,GAAO,CAAE,SAAAL,GAAU,EAEnCM,GAAa,YAAY,EAEzB,MAAMC,EAAYC,GAAmB,WAAY,EAAE,EAC7CC,EAAmBD,GAAmB,SAAU,EAAE,EAElDE,EAAYC,GAAiC,CACjD,KAAM,OACN,QAAS,MAAC,CACX,EAEK,CAAE,UAAAC,EAAW,QAAAC,CAAQ,EAAIC,GAAa,EACtC,CAAE,eAAAC,EAAgB,aAAAC,CAAa,EAAIC,GAAkB,EAGrDC,EAAgC,CACpC,kBAAmBC,GAAgB,EACnC,kBAAmBC,GAAgB,CACrC,EAEMC,EAASC,GAAM,IAAM,CACnB,KAAA,CAAE,UAAAV,EAAW,QAAAC,CAAY,EAAAU,GAAO,IAAI,uBAAwBb,EAAU,MAAO,WAAW,EACxF,CAACc,EAAUC,CAAI,EAAIC,GAASjB,EAAiB,MAAgBkB,GAAAA,EAAM,SAAS,GAAG,CAAC,EAChFC,EAASJ,EAAS,IAAII,GAAUA,EAAO,QAAQ,KAAM,GAAG,CAAC,EAIzDC,EADoB,CAAC,mBAAmB,EACR,OAAOC,GAAY,CAACL,EAAK,SAASK,CAAQ,CAAC,EAiB1ET,MAf+B,CACpC,SAAU,CACR,MAAON,EAAe,OAASH,EAC/B,MAAOI,EAAa,OAASH,CAC/B,EACA,YAAa,CACX,SAAUN,EAAU,KACtB,EACA,MAAO,CACL,OAAAqB,EACA,KAAAH,EACA,YAAAI,CAAA,CAEJ,CAEO,CACR,EAEK,CAAE,KAAAE,EAAM,MAAAC,EAAO,QAAAC,EAAS,OAAAC,EAAQ,MAAAC,CAAM,EAAIC,GAA+Bf,EAAQ,CAAE,SAAArB,GAAU,EAE7F,CAAE,OAAAqC,CAAO,EAAIC,GAA0B,EACvCC,EAAkE,KAAO,CAC7E,WAAYF,EAAO,KAAA,GAEf,CAAE,MAAAG,CAAU,EAAAC,GAA0BtC,EAAOoC,CAAqB,EAClEG,EAAUxC,GAAS,KAAO,CAC9B,MAAO,CACL,uBAAwBsC,EAAM,KAAA,CAChC,EACA,EAEF,IAAIG,EAAmB,GAEjB,MAAAC,EAA6BlF,GAAS,IAAM,CAChD,GAAI,GAACkD,EAAU,OAAS,CAACC,EAAQ,OAIjC,IAAI8B,EAAkB,CACDA,EAAA,GACnB,MAAA,CAGFjC,EAAU,MAAQ,CAChB,KAAM,QACN,UAAWE,EAAU,MACrB,QAASC,EAAQ,KACnB,IACC,GAAI,EAEPgC,GAAM,CAACjC,EAAWC,CAAO,EAAG,IAAM+B,GAA4B,EAE9D,SAASE,GAA6B,SAChC,GAAA,CAACpC,EAAU,MACb,OAGI,KAAA,CAAE,UAAWqC,EAAU,QAASC,CAAW,EAAAzB,GAAO,IAAI,uBAAwBb,EAAU,MAAO,WAAW,IAE5GuC,EAAArC,EAAU,QAAV,YAAAqC,EAAiB,aAAcF,EAAS,YACvBJ,EAAA,GACnB/B,EAAU,MAAQmC,KAGhBG,EAAArC,EAAQ,QAAR,YAAAqC,EAAe,aAAcF,EAAO,YACnBL,EAAA,GACnB9B,EAAQ,MAAQmC,EAClB,CAGF,OAAAH,GAAM,IAAMnC,EAAU,MAAO,IAAMoC,GAAsB,EAEpCA,EAAA,6FA3LG,OAAAK,EAAAvC,CAAA,GAAauC,EAAOtC,CAAA,OAA5CuC,EAsDmBC,EAAA,OAtD2B,MAAM,QAAA,GACvC,SACT,IAAgC,CAAhCC,EAAgCH,EAAAI,EAAA,EAAA,CAAlB,OAAQtD,EAAM,OAAA,KAAA,EAAA,CAAA,QAAA,CAAA,CAAA,aAG9B,IAgDM,CAhDNuD,EAgDM,MAhDNC,GAgDM,CA/CJD,EAYM,MAZNE,GAYM,CAXJJ,EAIUK,EAAA,CAJD,MAAM,YAAU,CACZ,QAAOC,EAChB,CAAmE,CAD/C,GAAAC,KAAE,CACtBP,EAAmEH,EAAAW,EAAA,EAAA,CAA3C,GAAAD,EAAW,SAAUV,EAAS5C,CAAA,0CAATA,EAAS,MAAAwD,EAAA,MAAE,SAAA,sCAI5DT,EAIUK,EAAA,CAJD,MAAM,UAAQ,CACV,QAAOC,EAChB,CAAmE,CAD/C,GAAAC,KAAE,CACtBP,EAAmEH,EAAAa,EAAA,EAAA,CAAlD,GAAAH,EAAW,SAAUV,EAAgB1C,CAAA,0CAAhBA,EAAgB,MAAAsD,EAAA,MAAE,SAAA,wCAK9DP,EAKM,MAAA,SALG,QAAJ,IAAIrD,EAAQ,MAAM8D,GAAA,CAAA,6BAAqCvB,EAAA,MAAQ,KAAK,CAAA,CAAA,GACvEY,EAA4GH,EAAAe,EAAA,EAAA,CAA1E,aAAYf,EAASvC,CAAA,2CAATA,EAAS,MAAAmD,EAAA,MAAU,WAAUZ,EAAOtC,CAAA,yCAAPA,EAAO,MAAAkD,EAAA,MAAG,OAAA1C,EAAM,MAAE,eAAAH,CAAA,6CAC7FsC,EAEM,MAFNW,GAEM,CADJb,EAAwDH,EAAAiB,EAAA,EAAA,CAA9B,WAAAjB,EAAAzC,CAAA,EAAU,MAAV,sBAAA2D,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAAN,GAAAZ,EAAAzC,CAAA,EAAU,MAAKqD,GAAG,IAAKZ,EAAG1F,CAAA,sCAIxC0F,EAAAlB,CAAA,GAAWkB,EAAKhB,CAAA,OAC9BiB,EAIkBkB,EAAA,CAAA,IAAA,GAAA,CAHL,UAAQ,IAEnBD,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,IAFmB,kBAEnB,CAAA,gBAIJE,GAiBWC,GAAA,CAAA,IAAA,GAAA,CAhBOrB,EAAKhB,CAAA,OACnBiB,EAIkBkB,EAAA,CAAA,IAAA,GAAA,CAHL,UAAQ,IAEnBD,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,IAFmB,aAEnB,CAAA,oBAIJf,EAAkFH,EAAAsB,EAAA,EAAA,CAAzD,MAAM,mBAAoB,OAAAtB,EAAMjB,CAAA,EAAE,aAAAiB,EAAUvC,CAAA,EAAE,WAAAuC,EAAQtC,CAAA,CAAA,6CAGvEsC,EAAAjB,CAAA,EAAO,YADfkB,EAKEsB,EAAA,OAHQ,KAAMvB,EAAIpB,CAAA,sCAAJA,EAAI,MAAAgC,EAAA,MACV,MAAOZ,EAAKnB,CAAA,uCAALA,EAAK,MAAA+B,EAAA,MACpB,MAAM,eAAA", "x_google_ignoreList": [0]}