{"version": 3, "file": "Automations-DzBlUaTQ.js", "sources": ["../../src/components/AutomationCard.vue", "../../src/pages/Automations.vue"], "sourcesContent": ["<template>\n  <p-card class=\"automation-card\">\n    <p-content>\n      <p-content secondary>\n        <div class=\"automation-card__header\">\n          <p-link class=\"automation-card__name\" :to=\"routes.automation(automation.id)\">\n            {{ automation.name }}\n          </p-link>\n          <div class=\"automation-card__header-actions\">\n            <AutomationToggle :automation=\"automation\" @update=\"emit('update')\" />\n            <AutomationMenu :automation=\"automation\" @delete=\"emit('update')\" />\n          </div>\n        </div>\n        <template v-if=\"automation.description\">\n          <p class=\"automation-card__description\">\n            {{ automation.description }}\n          </p>\n        </template>\n      </p-content>\n\n      <p-content secondary>\n        <span class=\"automation-card__label\">Trigger</span>\n        <AutomationTriggerDescription :trigger=\"automation.trigger\" />\n      </p-content>\n\n      <p-content secondary>\n        <span class=\"automation-card__label\">{{ toPluralString('Action', automation.actions.length) }}</span>\n        <template v-for=\"action in automation.actions\" :key=\"action.id\">\n          <p-card><AutomationActionDescription :action=\"action\" /></p-card>\n        </template>\n      </p-content>\n    </p-content>\n  </p-card>\n</template>\n\n<script lang=\"ts\" setup>\n  import { toPluralString } from '@prefecthq/prefect-design'\n  import { AutomationMenu, AutomationToggle, AutomationTriggerDescription, AutomationActionDescription, useWorkspaceRoutes } from '@prefecthq/prefect-ui-library'\n  import { Automation } from '@/types/automation'\n\n  defineProps<{\n    automation: Automation,\n  }>()\n\n  const emit = defineEmits<{\n    (event: 'update'): void,\n  }>()\n\n  const routes = useWorkspaceRoutes()\n</script>\n\n<style>\n.automation-card__header { @apply\n  flex\n  gap-2\n  items-center\n  justify-between\n}\n\n.automation-card__header-actions { @apply\n  flex\n  gap-2\n  items-center\n}\n\n.automation-card__name { @apply\n  text-lg\n}\n\n.automation-card__description { @apply\n  text-sm\n}\n\n.automation-card__label { @apply\n  font-medium\n  mr-2\n}\n</style>", "<template>\n  <p-layout-default class=\"automations\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\">\n        <template #after-crumbs>\n          <p-button size=\"sm\" icon=\"PlusIcon\" :to=\"routes.automationCreate()\" />\n        </template>\n\n        <template v-if=\"!empty\" #actions>\n          <DocumentationButton :to=\"localization.docs.automations\">\n            Documentation\n          </DocumentationButton>\n        </template>\n      </PageHeading>\n    </template>\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <AutomationsPageEmptyState />\n      </template>\n      <template v-else>\n        <ResultsCount :count=\"automations.length\" label=\"automation\" />\n\n        <p-virtual-scroller :items=\"automations\" class=\"automations-list\">\n          <template #default=\"{ item: automation }\">\n            <AutomationCard :automation=\"automation\" @update=\"subscription.refresh\" />\n          </template>\n        </p-virtual-scroller>\n      </template>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeading, ResultsCount, AutomationsPageEmptyState, DocumentationButton, localization, useWorkspaceRoutes } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import AutomationCard from '@/components/AutomationCard.vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { usePrefectApi } from '@/compositions/usePrefectApi'\n\n  const routes = useWorkspaceRoutes()\n\n  usePageTitle('Automations')\n\n  const crumbs = [{ text: 'Automations' }]\n  const api = usePrefectApi()\n\n  const subscription = useSubscription(api.automations.getAutomations)\n  const automations = computed(() => subscription.response ?? [])\n\n  const loaded = computed(() => subscription.executed)\n  const empty = computed(() => automations.value.length === 0)\n</script>\n\n<style>\n.automations {\n  --virtual-scroller-item-gap: theme('spacing.2')\n}\n</style>"], "names": ["emit", "__emit", "routes", "useWorkspaceRoutes", "_createBlock", "_component_p_card", "_createVNode", "_component_p_content", "_createElementVNode", "_hoisted_1", "_component_p_link", "_unref", "automation", "_hoisted_2", "AutomationToggle", "AutomationMenu", "_openBlock", "_createElementBlock", "_hoisted_3", "_cache", "AutomationTriggerDescription", "_hoisted_4", "_toDisplayString", "toPluralString", "_Fragment", "action", "AutomationActionDescription", "usePageTitle", "crumbs", "api", "usePrefectApi", "subscription", "useSubscription", "automations", "computed", "loaded", "empty", "_component_p_layout_default", "PageHeading", "_createSlots", "_component_p_button", "DocumentationButton", "localization", "AutomationsPageEmptyState", "ResultsCount", "_component_p_virtual_scroller", "_withCtx", "AutomationCard"], "mappings": "onBA4CE,MAAMA,EAAOC,EAIPC,EAASC,EAAmB,4EA/ClC,EAAAC,EA+BSC,EAAA,CA/BD,MAAM,mBAAiB,WAC7B,IA6BY,CA7BZC,EA6BYC,EAAA,KAAA,WA5BV,IAeY,CAfZD,EAeYC,EAAA,CAfD,UAAA,IAAS,WAClB,IAQM,CARNC,EAQM,MARNC,EAQM,CAPJH,EAESI,EAAA,CAFD,MAAM,wBAAyB,GAAIC,EAAMT,CAAA,EAAC,WAAWU,EAAAA,WAAW,EAAE,CAAA,aACxE,IAAqB,CAAlBA,EAAAA,EAAAA,EAAAA,WAAW,IAAI,EAAA,CAAA,CAAA,kBAEpBJ,EAGM,MAHNK,EAGM,CAFJP,EAAsEK,EAAAG,CAAA,EAAA,CAAnD,WAAYF,EAAU,WAAG,wBAAQZ,EAAI,QAAA,2BACxDM,EAAoEK,EAAAI,CAAA,EAAA,CAAnD,WAAYH,EAAU,WAAG,wBAAQZ,EAAI,QAAA,EAAA,6BAG1CY,EAAAA,WAAW,aACzBI,EAAA,EAAAC,EAEI,IAFJC,EACKN,EAAAA,EAAAA,WAAW,WAAW,EAAA,CAAA,oBAK/BN,EAGYC,EAAA,CAHD,UAAA,IAAS,WAClB,IAAmD,CAAnDY,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAAX,EAAmD,OAA7C,CAAA,MAAM,0BAAyB,UAAO,EAAA,GAC5CF,EAA8DK,EAAAS,CAAA,EAAA,CAA/B,QAASR,EAAU,WAAC,OAAA,qCAGrDN,EAKYC,EAAA,CALD,UAAA,IAAS,WAClB,IAAqG,CAArGC,EAAqG,OAArGa,EAAwCC,EAAAX,EAAAY,CAAA,WAAyBX,EAAU,WAAC,QAAQ,MAAM,CAAA,EAAA,CAAA,GAC1FI,EAAA,EAAA,EAAAC,EAEWO,EAFgBZ,KAAAA,EAAAA,EAAAA,WAAW,QAArBa,QACfrB,EAAiEC,EAAA,CADd,IAAAoB,EAAO,EAAA,aAClD,IAAgD,CAAhDnB,EAAgDK,EAAAe,CAAA,EAAA,CAAlB,OAAAD,CAAc,EAAA,KAAA,EAAA,CAAA,QAAA,CAAA,CAAA,sFCY5D,MAAMvB,EAASC,EAAmB,EAElCwB,EAAa,aAAa,EAE1B,MAAMC,EAAS,CAAC,CAAE,KAAM,cAAe,EACjCC,EAAMC,EAAc,EAEpBC,EAAeC,EAAgBH,EAAI,YAAY,cAAc,EAC7DI,EAAcC,EAAS,IAAMH,EAAa,UAAY,CAAA,CAAE,EAExDI,EAASD,EAAS,IAAMH,EAAa,QAAQ,EAC7CK,EAAQF,EAAS,IAAMD,EAAY,MAAM,SAAW,CAAC,iGAlD3D,EAAA7B,EA4BmBiC,EAAA,CA5BD,MAAM,eAAa,CACxB,SACT,IAUc,CAVd/B,EAUcK,EAAA2B,CAAA,EAAA,CAVA,OAAAV,CAAA,EAAcW,EAAA,CACf,iBACT,IAAsE,CAAtEjC,EAAsEkC,EAAA,CAA5D,KAAK,KAAK,KAAK,WAAY,GAAI7B,EAAMT,CAAA,EAAC,iBAAgB,CAAA,yBAGjDkC,EAAK,mBAAG,eACvB,IAEsB,CAFtB9B,EAEsBK,EAAA8B,CAAA,EAAA,CAFA,GAAI9B,EAAA+B,CAAA,EAAa,KAAK,WAAA,aAAa,IAEzDvB,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAFyD,iBAEzD,CAAA,yDAIN,IAaW,CAbKgB,EAAM,WAAtBlB,EAaWO,EAAA,CAAA,IAAA,GAAA,CAZOY,EAAK,WACnBhC,EAA6BO,EAAAgC,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA,QAE/B1B,EAQWO,EAAA,CAAA,IAAA,GAAA,CAPTlB,EAA+DK,EAAAiC,CAAA,EAAA,CAAhD,MAAOX,EAAW,MAAC,OAAQ,MAAM,gCAEhD3B,EAIqBuC,EAAA,CAJA,MAAOZ,EAAW,MAAE,MAAM,kBAAA,GAClC,QAAOa,EAChB,CAA0E,CAAA,KADhDlC,KAAU,CACpCN,EAA0EyC,EAAA,CAAzD,WAAAnC,EAAyB,SAAQD,EAAYoB,CAAA,EAAC"}