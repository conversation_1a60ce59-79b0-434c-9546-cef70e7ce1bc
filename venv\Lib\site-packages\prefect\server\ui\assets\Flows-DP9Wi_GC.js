import{d as f,f as i,g as _,h as o,i as w,c as s,o as t,j as n,q as C,a as k,F as b,m as a,bM as h,bN as y,k as v,bO as x}from"./index-g6FNXcTE.js";import{u as F}from"./usePageTitle-DEr56mEx.js";const V=f({__name:"Flows",setup(g){const c=i(),l={interval:3e4},e=_(c.flows.getFlowsCount,[{}],l),r=o(()=>e.response??0),u=o(()=>e.executed&&r.value===0),p=o(()=>e.executed),m=()=>{e.refresh()};return F("Flows"),(B,D)=>{const d=w("p-layout-default");return t(),s(d,{class:"flows"},{header:n(()=>[v(a(x))]),default:n(()=>[p.value?(t(),C(b,{key:0},[u.value?(t(),s(a(h),{key:0})):(t(),s(a(y),{key:1,selectable:"",onDelete:m}))],64)):k("",!0)]),_:1})}}});export{V as default};
//# sourceMappingURL=Flows-DP9Wi_GC.js.map
