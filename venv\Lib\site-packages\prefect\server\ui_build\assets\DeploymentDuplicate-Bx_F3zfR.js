import{d as D,f as v,W as h,g as w,h as s,i as C,c as g,a as b,o as k,j as l,k as r,m as c,c5 as x,c6 as I,K as p,c7 as m,be as j,L as B}from"./index-g6FNXcTE.js";import{u as E}from"./usePageTitle-DEr56mEx.js";const W=D({__name:"DeploymentDuplicate",setup(N){const a=v(),u=h("deploymentId"),d=w(a.deployments.getDeployment,[u.value],{}),e=s(()=>d.response);function i(t){return"name"in t}async function y(t){try{if(!i(t))throw new Error("Invalid request");const n=await a.deployments.createDeployment(t);p("Deployment created","success"),m.push(j.deployment(n.id))}catch(n){const o=B(n,"Error creating deployment");p(o,"error"),console.warn(n)}}function f(){m.back()}const _=s(()=>e.value?`Duplicate Deployment: ${e.value.name}`:"Duplicate Deployment");return E(_),(t,n)=>{const o=C("p-layout-default");return e.value?(k(),g(o,{key:0,class:"deployment-edit"},{header:l(()=>[r(c(I),{deployment:e.value},null,8,["deployment"])]),default:l(()=>[r(c(x),{deployment:e.value,mode:"duplicate",onCancel:f,onSubmit:y},null,8,["deployment"])]),_:1})):b("",!0)}}});export{W as default};
//# sourceMappingURL=DeploymentDuplicate-Bx_F3zfR.js.map
