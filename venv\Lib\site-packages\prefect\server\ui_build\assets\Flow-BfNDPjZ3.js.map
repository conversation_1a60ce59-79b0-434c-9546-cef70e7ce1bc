{"version": 3, "file": "Flow-BfNDPjZ3.js", "sources": ["../../src/components/FlowStats.vue", "../../src/pages/Flow.vue"], "sourcesContent": ["<template>\n  <div class=\"flow-stats\">\n    <div class=\"flow-stats__cards\">\n      <FlowRunHistoryCard :filter=\"flowRunsFilter\" />\n\n      <CumulativeTaskRunsCard :filter=\"taskRunsFilter\" />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\n  import {\n    CumulativeTaskRunsCard,\n    FlowRunHistoryCard,\n    subscriptionIntervalKey,\n    mapper,\n    FlowStatsFilter,\n    WorkspaceDashboardFilter\n  } from '@prefecthq/prefect-ui-library'\n  import { secondsInWeek, secondsToMilliseconds } from 'date-fns'\n  import { computed, provide, toRefs } from 'vue'\n\n  const props = defineProps<{\n    flowId: string,\n  }>()\n\n  const filter: WorkspaceDashboardFilter = {\n    range: { type: 'span', seconds: -secondsInWeek },\n    tags: [],\n  }\n\n  const { flowId } = toRefs(props)\n\n  const flowStats = computed<FlowStatsFilter>(() => {\n    return {\n      flowId: flowId.value,\n      range: filter.range,\n    }\n  })\n\n  provide(subscriptionIntervalKey, {\n    interval: secondsToMilliseconds(30),\n  })\n\n  const flowRunsFilter = computed(() => mapper.map('FlowStatsFilter', flowStats.value, 'FlowRunsFilter'))\n  const taskRunsFilter = computed(() => mapper.map('FlowStatsFilter', flowStats.value, 'TaskRunsFilter'))\n</script>\n\n<style>\n.flow-stats { @apply\n  w-full\n  flex\n  flex-col\n  gap-4\n  items-center\n}\n\n.flow-stats__cards { @apply\n  w-full\n  grid\n  gap-5\n  sm:grid-cols-3\n  md:grid-cols-[2fr_1fr]\n}\n</style>", "<template>\n  <p-layout-default class=\"flow\">\n    <template #header>\n      <PageHeadingFlow v-if=\"flow\" :flow=\"flow\" @delete=\"deleteFlow\" />\n    </template>\n\n    <FlowStats v-if=\"flow\" :flow-id=\"flow.id\" />\n\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #details>\n        <FlowDetails v-if=\"flow\" :flow=\"flow\" />\n      </template>\n\n      <template #deployments>\n        <DeploymentList :filter=\"deploymentsFilter\" prefix=\"deployments\" />\n      </template>\n\n      <template #runs>\n        <FlowRunFilteredList :filter=\"flowRunsFilter\" selectable prefix=\"runs\" />\n      </template>\n    </p-tabs>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { DeploymentList, PageHeadingFlow, FlowDetails, FlowRunFilteredList, useWorkspaceApi, useFlowRunsFilter, useDeploymentsFilter } from '@prefecthq/prefect-ui-library'\n  import { useSubscription, useRouteParam, useRouteQueryParam } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import FlowStats from '@/components/FlowStats.vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router/routes'\n\n  const api = useWorkspaceApi()\n  const flowId = useRouteParam('flowId')\n  const flowIds = computed(() => [flowId.value])\n  const router = useRouter()\n  const tab = useRouteQueryParam('tab', 'Runs')\n  const tabs = ['Runs', 'Deployments', 'Details']\n\n  const subscriptionOptions = {\n    interval: 300000,\n  }\n\n  const flowSubscription = useSubscription(api.flows.getFlow, [flowId.value], subscriptionOptions)\n  const flow = computed(() => flowSubscription.response)\n\n  const { filter: flowRunsFilter } = useFlowRunsFilter({\n    flows: {\n      id: flowIds,\n    },\n  })\n\n  const { filter: deploymentsFilter } = useDeploymentsFilter({\n    flows: {\n      id: flowIds,\n    },\n  })\n\n  function deleteFlow(): void {\n    router.push(routes.flows())\n  }\n\n  const title = computed(() => {\n    if (!flow.value) {\n      return 'Flow'\n    }\n    return `Flow: ${flow.value.name}`\n  })\n  usePageTitle(title)\n</script>"], "names": ["props", "__props", "filter", "flowId", "toRefs", "flowStats", "computed", "provide", "subscriptionIntervalKey", "secondsToMilliseconds", "flowRunsFilter", "mapper", "taskRunsFilter", "_openBlock", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_unref", "FlowRunHistoryCard", "CumulativeTaskRunsCard", "api", "useWorkspaceApi", "useRouteParam", "flowIds", "router", "useRouter", "tab", "useRouteQueryParam", "tabs", "subscriptionOptions", "flowSubscription", "useSubscription", "flow", "useFlowRunsFilter", "deploymentsFilter", "useDeploymentsFilter", "deleteFlow", "routes", "title", "usePageTitle", "_createBlock", "_component_p_layout_default", "PageHeadingFlow", "FlowStats", "_component_p_tabs", "$event", "FlowDetails", "DeploymentList", "FlowRunFilteredList"], "mappings": "icAsBE,MAAMA,EAAQC,EAIRC,EAAmC,CACvC,MAAO,CAAE,KAAM,OAAQ,QAAS,OAAe,CAEjD,EAEM,CAAE,OAAAC,CAAA,EAAWC,EAAOJ,CAAK,EAEzBK,EAAYC,EAA0B,KACnC,CACL,OAAQH,EAAO,MACf,MAAOD,EAAO,KAChB,EACD,EAEDK,EAAQC,EAAyB,CAC/B,SAAUC,EAAsB,EAAE,CAAA,CACnC,EAEK,MAAAC,EAAiBJ,EAAS,IAAMK,EAAO,IAAI,kBAAmBN,EAAU,MAAO,gBAAgB,CAAC,EAChGO,EAAiBN,EAAS,IAAMK,EAAO,IAAI,kBAAmBN,EAAU,MAAO,gBAAgB,CAAC,gBA5CtGQ,EAAA,EAAAC,EAMM,MANNC,EAMM,CALJC,EAIM,MAJNC,EAIM,CAHJC,EAA+CC,EAAAC,CAAA,EAAA,CAA1B,OAAQV,EAAc,KAAA,EAAA,KAAA,EAAA,CAAA,QAAA,CAAA,EAE3CQ,EAAmDC,EAAAE,CAAA,EAAA,CAA1B,OAAQT,EAAc,OAAA,KAAA,EAAA,CAAA,QAAA,CAAA,uCC4BnD,MAAMU,EAAMC,EAAgB,EACtBpB,EAASqB,EAAc,QAAQ,EAC/BC,EAAUnB,EAAS,IAAM,CAACH,EAAO,KAAK,CAAC,EACvCuB,EAASC,EAAU,EACnBC,EAAMC,EAAmB,MAAO,MAAM,EACtCC,EAAO,CAAC,OAAQ,cAAe,SAAS,EAExCC,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAAmBC,EAAgBX,EAAI,MAAM,QAAS,CAACnB,EAAO,KAAK,EAAG4B,CAAmB,EACzFG,EAAO5B,EAAS,IAAM0B,EAAiB,QAAQ,EAE/C,CAAE,OAAQtB,CAAe,EAAIyB,EAAkB,CACnD,MAAO,CACL,GAAIV,CAAA,CACN,CACD,EAEK,CAAE,OAAQW,CAAkB,EAAIC,EAAqB,CACzD,MAAO,CACL,GAAIZ,CAAA,CACN,CACD,EAED,SAASa,GAAmB,CACnBZ,EAAA,KAAKa,EAAO,OAAO,CAAA,CAGtB,MAAAC,EAAQlC,EAAS,IAChB4B,EAAK,MAGH,SAASA,EAAK,MAAM,IAAI,GAFtB,MAGV,EACD,OAAAO,EAAaD,CAAK,+DApElB,EAAAE,EAoBmBC,EAAA,CApBD,MAAM,QAAM,CACjB,SACT,IAAiE,CAA1CT,EAAI,WAA3BQ,EAAiEvB,EAAAyB,CAAA,EAAA,OAAnC,KAAMV,EAAI,MAAG,SAAQI,CAAA,yCAGrD,IAA4C,CAA3BJ,EAAI,WAArBQ,EAA4CG,EAAA,OAApB,UAASX,EAAI,MAAC,EAAA,gCAEtChB,EAYS4B,EAAA,CAZO,SAAU3B,EAAGS,CAAA,0CAAHA,EAAG,MAAAmB,EAAA,MAAG,KAAAjB,CAAA,GACnB,UACT,IAAwC,CAArBI,EAAI,WAAvBQ,EAAwCvB,EAAA6B,CAAA,EAAA,OAAd,KAAMd,EAAI,KAAA,+BAG3B,cACT,IAAmE,CAAnEhB,EAAmEC,EAAA8B,CAAA,EAAA,CAAlD,OAAQ9B,EAAiBiB,CAAA,EAAE,OAAO,aAAA,uBAG1C,OACT,IAAyE,CAAzElB,EAAyEC,EAAA+B,CAAA,EAAA,CAAnD,OAAQ/B,EAAcT,CAAA,EAAE,WAAA,GAAW,OAAO,MAAA"}