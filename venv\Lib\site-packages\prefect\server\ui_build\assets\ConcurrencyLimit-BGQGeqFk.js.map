{"version": 3, "file": "ConcurrencyLimit-BGQGeqFk.js", "sources": ["../../src/pages/ConcurrencyLimit.vue"], "sourcesContent": ["<template>\n  <p-layout-well class=\"concurrencyLimit\">\n    <template #header>\n      <PageHeadingConcurrencyLimit v-if=\"concurrencyLimit\" :concurrency-limit=\"concurrencyLimit\" @delete=\"deleteConcurrencyLimit\" />\n    </template>\n\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #details>\n        <ConcurrencyLimitDetails v-if=\"concurrencyLimit\" :concurrency-limit=\"concurrencyLimit\" />\n      </template>\n      <template #active-task-runs>\n        <ConcurrencyLimitActiveRuns v-if=\"concurrencyLimit?.activeSlots\" :active-slots=\"concurrencyLimit.activeSlots\" />\n      </template>\n    </p-tabs>\n\n    <template #well>\n      <ConcurrencyLimitDetails v-if=\"concurrencyLimit\" alternate :concurrency-limit=\"concurrencyLimit\" />\n    </template>\n  </p-layout-well>\n</template>\n\n<script lang=\"ts\" setup>\n  import { media } from '@prefecthq/prefect-design'\n  import { PageHeadingConcurrencyLimit, ConcurrencyLimitDetails, ConcurrencyLimitActiveRuns, useTabs, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam, useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n\n  const api = useWorkspaceApi()\n  const concurrencyLimitId = useRouteParam('concurrencyLimitId')\n  const router = useRouter()\n\n  const computedTabs = computed(() => [\n    { label: 'Details', hidden: media.xl },\n    { label: 'Active Task Runs' },\n  ])\n  const tab = useRouteQueryParam('tab', 'Details')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  const subscriptionOptions = {\n    interval: 300000,\n  }\n\n  const concurrencyLimitSubscription = useSubscription(api.concurrencyLimits.getConcurrencyLimit, [concurrencyLimitId.value], subscriptionOptions)\n  const concurrencyLimit = computed(() => concurrencyLimitSubscription.response)\n\n\n  function deleteConcurrencyLimit(): void {\n    router.push(routes.concurrencyLimits())\n  }\n\n  const title = computed<string>(() => {\n    if (!concurrencyLimit.value) {\n      return 'Concurrency Limit'\n    }\n\n    return `Concurrency Limit: ${concurrencyLimit.value.tag}`\n  })\n\n  usePageTitle(title)\n</script>"], "names": ["api", "useWorkspaceApi", "concurrencyLimitId", "useRouteParam", "router", "useRouter", "computedTabs", "computed", "media", "tab", "useRouteQueryParam", "tabs", "useTabs", "subscriptionOptions", "concurrencyLimitSubscription", "useSubscription", "concurrencyLimit", "deleteConcurrencyLimit", "routes", "title", "usePageTitle", "_createBlock", "_component_p_layout_well", "_unref", "PageHeadingConcurrencyLimit", "ConcurrencyLimitDetails", "_createVNode", "_component_p_tabs", "$event", "_a", "ConcurrencyLimitActiveRuns"], "mappings": "oRA8BE,MAAMA,EAAMC,EAAgB,EACtBC,EAAqBC,EAAc,oBAAoB,EACvDC,EAASC,EAAU,EAEnBC,EAAeC,EAAS,IAAM,CAClC,CAAE,MAAO,UAAW,OAAQC,EAAM,EAAG,EACrC,CAAE,MAAO,kBAAmB,CAAA,CAC7B,EACKC,EAAMC,EAAmB,MAAO,SAAS,EACzC,CAAE,KAAAC,CAAS,EAAAC,EAAQN,EAAcG,CAAG,EAEpCI,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAA+BC,EAAgBf,EAAI,kBAAkB,oBAAqB,CAACE,EAAmB,KAAK,EAAGW,CAAmB,EACzIG,EAAmBT,EAAS,IAAMO,EAA6B,QAAQ,EAG7E,SAASG,GAA+B,CAC/Bb,EAAA,KAAKc,EAAO,mBAAmB,CAAA,CAGlC,MAAAC,EAAQZ,EAAiB,IACxBS,EAAiB,MAIf,sBAAsBA,EAAiB,MAAM,GAAG,GAH9C,mBAIV,EAED,OAAAI,EAAaD,CAAK,4DA5DlB,EAAAE,EAiBgBC,EAAA,CAjBD,MAAM,oBAAkB,CAC1B,SACT,IAA8H,CAA3FN,EAAgB,WAAnDK,EAA8HE,EAAAC,CAAA,EAAA,OAAxE,oBAAmBR,EAAgB,MAAG,SAAQC,CAAA,4CAY3F,OACT,IAAmG,CAApED,EAAgB,WAA/CK,EAAmGE,EAAAE,CAAA,EAAA,OAAlD,UAAA,GAAW,oBAAmBT,EAAgB,KAAA,sDAVjG,IAOS,CAPTU,EAOSC,EAAA,CAPO,SAAUJ,EAAGd,CAAA,0CAAHA,EAAG,MAAAmB,EAAA,MAAG,KAAML,EAAIZ,CAAA,CAAA,GAC7B,UACT,IAAyF,CAA1DK,EAAgB,WAA/CK,EAAyFE,EAAAE,CAAA,EAAA,OAAvC,oBAAmBT,EAAgB,KAAA,4CAE5E,qBACT,IAAgH,OAAA,QAA9Ea,EAAAb,EAAA,QAAA,MAAAa,EAAkB,iBAApDR,EAAgHE,EAAAO,CAAA,EAAA,OAA9C,eAAcd,EAAgB,MAAC,WAAA"}