import{d as T,f as U,W as d,h as o,g as f,bu as D,aI as I,al as S,bt as F,i as p,c as L,a as V,o as Y,j as a,k as u,m as e,a6 as j,bz as z,d3 as A,d4 as v,cX as G,d5 as O}from"./index-g6FNXcTE.js";import{u as X}from"./usePageTitle-DEr56mEx.js";const J=T({__name:"WorkPoolQueue",setup(Z){const c=U(),l=d("workPoolName"),_=o(()=>[l.value]),r=d("workPoolQueueName"),b=o(()=>[r.value]),k={interval:3e5},i=f(c.workPoolQueues.getWorkPoolQueueByName,[l.value,r.value],k),t=o(()=>i.response),P=f(c.workPools.getWorkPoolByName,[l.value],k),g=o(()=>P.response),w=o(()=>{var n;return((n=g.value)==null?void 0:n.type)==="prefect-agent"}),q=o(()=>t.value?`Your work pool ${t.value.name} is ready to go!`:"Your work queue is ready to go!"),y=o(()=>`prefect ${w.value?"agent":"worker"} start --pool "${l.value}" --work-queue "${r.value}"`),N=o(()=>`Work queues are scoped to a work pool to allow ${w.value?"agents":"workers"} to pull from groups of queues with different priorities.`),{filter:Q}=D({workPoolQueues:{name:b},workPools:{name:_}}),W=o(()=>[{label:"Details",hidden:I.xl},{label:"Upcoming Runs"},{label:"Runs"}]),s=S("tab","Details"),{tabs:B}=F(W,s),h=o(()=>r.value?`Work Pool Queue: ${r.value}`:"Work Pool Queue");return X(h),(n,m)=>{const x=p("p-tabs"),C=p("p-layout-well"),$=p("p-layout-default");return t.value?(Y(),L($,{key:0,class:"work-pool-queue"},{header:a(()=>[u(e(O),{"work-pool-queue":t.value,"work-pool-name":e(l),onUpdate:e(i).refresh},null,8,["work-pool-queue","work-pool-name","onUpdate"])]),default:a(()=>[u(C,{class:"work-pool-queue__body"},{header:a(()=>[u(e(G),{command:y.value,title:q.value,subtitle:N.value},null,8,["command","title","subtitle"])]),well:a(()=>[u(e(v),{alternate:"","work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),default:a(()=>[u(x,{selected:e(s),"onUpdate:selected":m[0]||(m[0]=R=>j(s)?s.value=R:null),tabs:e(B)},{details:a(()=>[u(e(v),{"work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),"upcoming-runs":a(()=>[u(e(A),{"work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),runs:a(()=>[u(e(z),{filter:e(Q),prefix:"runs"},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})]),_:1})):V("",!0)}}});export{J as default};
//# sourceMappingURL=WorkPoolQueue-BQ8TQCPW.js.map
