{"version": 3, "file": "BlockView-CJM162TA.js", "sources": ["../../src/pages/BlockView.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"blockDocument\" class=\"block-view\">\n    <template #header>\n      <PageHeadingBlock :block-document=\"blockDocument\" @delete=\"routeToBlocks\" />\n    </template>\n\n    <BlockDocumentCard :block-document=\"blockDocument\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeadingBlock, BlockDocumentCard, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useSubscriptionWithDependencies, useRouteParam } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n\n  const api = useWorkspaceApi()\n  const router = useRouter()\n  const blockDocumentId = useRouteParam('blockDocumentId')\n  const blockDocumentSubscriptionsArgs = computed<Parameters<typeof api.blockDocuments.getBlockDocument> | null >(() => {\n    if (!blockDocumentId.value) {\n      return null\n    }\n\n    return [blockDocumentId.value]\n  })\n  const blockDocumentSubscription = useSubscriptionWithDependencies(api.blockDocuments.getBlockDocument, blockDocumentSubscriptionsArgs)\n  const blockDocument = computed(() => blockDocumentSubscription.response)\n\n  const routeToBlocks = (): void => {\n    router.push(routes.blocks())\n  }\n\n  const title = computed(() => {\n    if (!blockDocument.value) {\n      return 'Block'\n    }\n    return `Block: ${blockDocument.value.name}`\n  })\n  usePageTitle(title)\n</script>"], "names": ["api", "useWorkspaceApi", "router", "useRouter", "blockDocumentId", "useRouteParam", "blockDocumentSubscriptionsArgs", "computed", "blockDocumentSubscription", "useSubscriptionWithDependencies", "blockDocument", "routeToBlocks", "routes", "title", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingBlock", "BlockDocumentCard"], "mappings": "sOAkBE,MAAMA,EAAMC,EAAgB,EACtBC,EAASC,EAAU,EACnBC,EAAkBC,EAAc,iBAAiB,EACjDC,EAAiCC,EAAyE,IACzGH,EAAgB,MAId,CAACA,EAAgB,KAAK,EAHpB,IAIV,EACKI,EAA4BC,EAAgCT,EAAI,eAAe,iBAAkBM,CAA8B,EAC/HI,EAAgBH,EAAS,IAAMC,EAA0B,QAAQ,EAEjEG,EAAgB,IAAY,CACzBT,EAAA,KAAKU,EAAO,QAAQ,CAC7B,EAEMC,EAAQN,EAAS,IAChBG,EAAc,MAGZ,UAAUA,EAAc,MAAM,IAAI,GAFhC,OAGV,EACD,OAAAI,EAAaD,CAAK,+CAxCMH,EAAa,WAArCK,EAMmBC,EAAA,OANoB,MAAM,YAAA,GAChC,SACT,IAA4E,CAA5EC,EAA4EC,EAAAC,CAAA,EAAA,CAAzD,iBAAgBT,EAAa,MAAG,SAAQC,CAAA,yCAG7D,IAAqD,CAArDM,EAAqDC,EAAAE,CAAA,EAAA,CAAjC,iBAAgBV,EAAa,OAAA,KAAA,EAAA,CAAA,gBAAA,CAAA,CAAA"}