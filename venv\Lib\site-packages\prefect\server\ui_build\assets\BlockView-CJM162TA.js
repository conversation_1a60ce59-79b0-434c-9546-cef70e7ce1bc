import{d,f as i,u as b,W as _,h as o,bF as f,i as v,c as D,a as B,o as h,j as c,k as s,m as u,cs as C,ct as w,be as g}from"./index-g6FNXcTE.js";import{u as x}from"./usePageTitle-DEr56mEx.js";const T=d({__name:"BlockView",setup(y){const n=i(),a=b(),t=_("blockDocumentId"),l=o(()=>t.value?[t.value]:null),r=f(n.blockDocuments.getBlockDocument,l),e=o(()=>r.response),m=()=>{a.push(g.blocks())},k=o(()=>e.value?`Block: ${e.value.name}`:"Block");return x(k),(V,I)=>{const p=v("p-layout-default");return e.value?(h(),D(p,{key:0,class:"block-view"},{header:c(()=>[s(u(w),{"block-document":e.value,onDelete:m},null,8,["block-document"])]),default:c(()=>[s(u(C),{"block-document":e.value},null,8,["block-document"])]),_:1})):B("",!0)}}});export{T as default};
//# sourceMappingURL=BlockView-CJM162TA.js.map
