import{d as t,W as n,f as k,g as l,u as m,V as p}from"./index-g6FNXcTE.js";const Q=t({__name:"WorkQueueToWorkPoolQueueRedirect",setup(w){const u=n("workQueueId"),s=k(),a=l(s.workQueues.getWorkQueue,[u]),o=m(),r=p();return a.promise().then(({response:e})=>{if(!e.workPoolName){o.replace(r.workPools());return}o.replace(r.workPoolQueue(e.workPoolName,e.name))}),()=>{}}});export{Q as default};
//# sourceMappingURL=WorkQueueToWorkPoolQueueRedirect-D-NUu8Nb-BoHCgzjo.js.map
