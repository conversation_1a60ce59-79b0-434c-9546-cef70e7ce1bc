{"version": 3, "file": "FlowRunCreate-D1bCfh4b.js", "sources": ["../../src/utilities/parameters.ts", "../../src/pages/FlowRunCreate.vue"], "sourcesContent": ["import { RouteParam } from '@prefecthq/vue-compositions'\nimport { LocationQueryValue } from 'vue-router'\n\nexport class JSONRouteParam extends RouteParam<Record<string, unknown>> {\n\n  protected override parse(value: LocationQueryValue): Record<string, unknown> {\n    return JSON.parse(decodeURIComponent(value ?? ''))\n  }\n\n  protected override format(value: Record<string, unknown>): LocationQueryValue {\n    return encodeURIComponent(JSON.stringify(value))\n  }\n}", "<template>\n  <p-layout-default v-if=\"deployment\">\n    <template #header>\n      <PageHeadingFlowRunCreate :deployment=\"deployment\" />\n    </template>\n\n    <FlowRunCreateForm :deployment :parameters :disabled @submit=\"createFlowRun\" @cancel=\"goBack\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { showToast } from '@prefecthq/prefect-design'\n  import { PageHeadingFlowRunCreate, ToastFlowRunCreate, useWorkspaceApi, useDeployment, FlowRunCreateForm, DeploymentFlowRunCreate, getApiErrorMessage } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam } from '@prefecthq/vue-compositions'\n  import { computed, h, ref } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n  import { JSONRouteParam } from '@/utilities/parameters'\n\n  const api = useWorkspaceApi()\n  const deploymentId = useRouteParam('deploymentId')\n  const router = useRouter()\n  const parameters = useRouteQueryParam('parameters', JSONRouteParam, undefined)\n  const { deployment } = useDeployment(deploymentId)\n  const disabled = ref(false)\n\n  const createFlowRun = async (request: DeploymentFlowRunCreate): Promise<void> => {\n    if (disabled.value) {\n      return\n    }\n\n    try {\n      disabled.value = true\n      const flowRun = await api.deployments.createDeploymentFlowRun(deploymentId.value, request)\n      const startTime = request.state?.stateDetails?.scheduledTime ?? undefined\n      const immediate = !startTime\n      const toastMessage = h(ToastFlowRunCreate, { flowRun, flowRunRoute: routes.flowRun, router, immediate, startTime })\n      showToast(toastMessage, 'success')\n      router.push(routes.deployment(deploymentId.value))\n    } catch (error) {\n      const message = getApiErrorMessage(error, 'Something went wrong trying to create a flow run')\n      showToast(message, 'error')\n      console.error(error)\n    } finally {\n      disabled.value = false\n    }\n  }\n\n  const goBack = (): void => {\n    router.back()\n  }\n\n  const title = computed<string>(() => {\n    if (!deployment.value) {\n      return 'Create Flow Run for Deployment'\n    }\n    return `Create Flow Run for Deployment: ${deployment.value.name}`\n  })\n  usePageTitle(title)\n</script>"], "names": ["JSONRouteParam", "RouteParam", "value", "api", "useWorkspaceApi", "deploymentId", "useRouteParam", "router", "useRouter", "parameters", "useRouteQueryParam", "deployment", "useDeployment", "disabled", "ref", "createFlowRun", "request", "flowRun", "startTime", "_b", "_a", "immediate", "toastMessage", "h", "ToastFlowRunCreate", "routes", "showToast", "error", "message", "getApiErrorMessage", "goBack", "title", "computed", "usePageTitle", "_unref", "_createBlock", "_component_p_layout_default", "_createVNode", "PageHeadingFlowRunCreate", "FlowRunCreateForm"], "mappings": "0PAGO,MAAMA,UAAuBC,CAAoC,CAEnD,MAAMC,EAAoD,CAC3E,OAAO,KAAK,MAAM,mBAAmBA,GAAS,EAAE,CAAC,CAAA,CAGhC,OAAOA,EAAoD,CAC5E,OAAO,mBAAmB,KAAK,UAAUA,CAAK,CAAC,CAAA,CAEnD,4CCQE,MAAMC,EAAMC,EAAgB,EACtBC,EAAeC,EAAc,cAAc,EAC3CC,EAASC,EAAU,EACnBC,EAAaC,EAAmB,aAAcV,EAAgB,MAAS,EACvE,CAAE,WAAAW,CAAA,EAAeC,EAAcP,CAAY,EAC3CQ,EAAWC,EAAI,EAAK,EAEpBC,EAAgB,MAAOC,GAAoD,SAC/E,GAAI,CAAAH,EAAS,MAIT,GAAA,CACFA,EAAS,MAAQ,GACjB,MAAMI,EAAU,MAAMd,EAAI,YAAY,wBAAwBE,EAAa,MAAOW,CAAO,EACnFE,IAAYC,GAAAC,EAAAJ,EAAQ,QAAR,YAAAI,EAAe,eAAf,YAAAD,EAA6B,gBAAiB,OAC1DE,EAAY,CAACH,EACbI,EAAeC,EAAEC,EAAoB,CAAE,QAAAP,EAAS,aAAcQ,EAAO,QAAS,OAAAlB,EAAQ,UAAAc,EAAW,UAAAH,CAAA,CAAW,EAClHQ,EAAUJ,EAAc,SAAS,EACjCf,EAAO,KAAKkB,EAAO,WAAWpB,EAAa,KAAK,CAAC,QAC1CsB,EAAO,CACR,MAAAC,EAAUC,EAAmBF,EAAO,kDAAkD,EAC5FD,EAAUE,EAAS,OAAO,EAC1B,QAAQ,MAAMD,CAAK,CAAA,QACnB,CACAd,EAAS,MAAQ,EAAA,CAErB,EAEMiB,EAAS,IAAY,CACzBvB,EAAO,KAAK,CACd,EAEMwB,EAAQC,EAAiB,IACxBrB,EAAW,MAGT,mCAAmCA,EAAW,MAAM,IAAI,GAFtD,gCAGV,EACD,OAAAsB,EAAaF,CAAK,wCA1DM,OAAAG,EAAUvB,CAAA,OAAlCwB,EAMmBC,EAAA,CAAA,IAAA,GAAA,CALN,SACT,IAAqD,CAArDC,EAAqDH,EAAAI,CAAA,EAAA,CAA1B,WAAYJ,EAAUvB,CAAA,GAAA,KAAA,EAAA,CAAA,YAAA,CAAA,CAAA,aAGnD,IAAgG,CAAhG0B,EAAgGH,EAAAK,CAAA,EAAA,CAA5E,WAAAL,EAAUvB,CAAA,EAAE,WAAAuB,EAAUzB,CAAA,EAAE,SAAAI,EAAQ,MAAE,SAAQE,EAAgB,SAAQe"}