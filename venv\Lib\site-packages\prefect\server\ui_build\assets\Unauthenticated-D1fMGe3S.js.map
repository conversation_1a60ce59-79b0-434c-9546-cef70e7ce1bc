{"version": 3, "file": "Unauthenticated-D1fMGe3S.js", "sources": ["../../src/pages/Unauthenticated.vue"], "sourcesContent": ["<template>\n  <div class=\"flex items-center justify-center min-h-screen\">\n    <div class=\"w-full max-w-[400px] p-8 m-4 bg-surface-raised rounded-lg shadow-lg\">\n      <p-heading tag=\"h1\" size=\"lg\" class=\"mb-6 text-center text-default\">\n        Login\n      </p-heading>\n      <form @submit.prevent=\"handleSubmit\" class=\"flex flex-col gap-4\">\n        <p-text-input\n          v-model=\"password\"\n          type=\"password\"\n          placeholder=\"admin:pass\"\n          :error=\"error\"\n          autofocus\n          class=\"w-full\"\n        />\n        <p-button\n          type=\"submit\"\n          :loading=\"loading\"\n          class=\"w-full\"\n        >\n          Login\n        </p-button>\n      </form>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { ref } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { usePrefectApi } from '@/compositions/usePrefectApi'\nimport { showToast } from '@prefecthq/prefect-design'\n\nconst props = defineProps<{\n  redirect?: string\n}>()\n\nconst password = ref('')\nconst loading = ref(false)\nconst error = ref('')\nconst router = useRouter()\nconst api = usePrefectApi()\n\nconst handleSubmit = async (): Promise<void> => {\n  if (loading.value) return\n\n  loading.value = true\n  error.value = ''\n\n  try {\n    localStorage.setItem('prefect-password', btoa(password.value))\n    api.admin.authCheck().then(status_code => {\n      if (status_code == 401) {\n        localStorage.removeItem('prefect-pasword')\n        showToast('Authentication failed.', 'error', { timeout: false })\n        if (router.currentRoute.value.name !== 'login') {\n          router.push({\n            name: 'login', \n            query: { redirect: router.currentRoute.value.fullPath }\n          })\n        }\n      } else {\n        api.health.isHealthy().then(healthy => {\n          if (!healthy) {\n            showToast(`Can't connect to Server API at ${config.baseUrl}. Check that it's accessible from your machine.`, 'error', { timeout: false })\n          }\n          router.push(props.redirect || '/')\n        })\n      }\n    })\n  } catch (e) {\n    localStorage.removeItem('prefect-password')\n    error.value = 'Invalid password'\n  } finally {\n    loading.value = false\n  }\n}\n</script>\n\n<style>\n</style>"], "names": ["props", "__props", "password", "ref", "loading", "error", "router", "useRouter", "api", "usePrefectApi", "handleSubmit", "status_code", "showToast", "healthy", "_openBlock", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_p_heading", "_cache", "_component_p_text_input", "$event", "_component_p_button"], "mappings": "yaAiCA,MAAMA,EAAQC,EAIRC,EAAWC,EAAI,EAAE,EACjBC,EAAUD,EAAI,EAAK,EACnBE,EAAQF,EAAI,EAAE,EACdG,EAASC,EAAU,EACnBC,EAAMC,EAAc,EAEpBC,EAAe,SAA2B,CAC9C,GAAI,CAAAN,EAAQ,MAEZ,CAAAA,EAAQ,MAAQ,GAChBC,EAAM,MAAQ,GAEV,GAAA,CACF,aAAa,QAAQ,mBAAoB,KAAKH,EAAS,KAAK,CAAC,EAC7DM,EAAI,MAAM,UAAY,EAAA,KAAoBG,GAAA,CACpCA,GAAe,KACjB,aAAa,WAAW,iBAAiB,EACzCC,EAAU,yBAA0B,QAAS,CAAE,QAAS,GAAO,EAC3DN,EAAO,aAAa,MAAM,OAAS,SACrCA,EAAO,KAAK,CACV,KAAM,QACN,MAAO,CAAE,SAAUA,EAAO,aAAa,MAAM,QAAS,CAAA,CACvD,GAGHE,EAAI,OAAO,UAAY,EAAA,KAAgBK,GAAA,CAChCA,GACOD,EAAA,kCAAkC,OAAO,OAAO,kDAAmD,QAAS,CAAE,QAAS,GAAO,EAEnIN,EAAA,KAAKN,EAAM,UAAY,GAAG,CAAA,CAClC,CACH,CACD,OACS,CACV,aAAa,WAAW,kBAAkB,EAC1CK,EAAM,MAAQ,kBAAA,QACd,CACAD,EAAQ,MAAQ,EAAA,EAEpB,2EA3EE,OAAAU,EAAA,EAAAC,EAuBM,MAvBNC,EAuBM,CAtBJC,EAqBM,MArBNC,EAqBM,CApBJC,EAEYC,EAAA,CAFD,IAAI,KAAK,KAAK,KAAK,MAAM,+BAAA,aAAgC,IAEpEC,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAFoE,SAEpE,CAAA,iBACAJ,EAgBO,OAAA,CAhBA,WAAgBP,EAAY,CAAA,SAAA,CAAA,EAAE,MAAM,qBAAA,GACzCS,EAOEG,EAAA,YANSpB,EAAQ,2CAARA,EAAQ,MAAAqB,GACjB,KAAK,WACL,YAAY,aACX,MAAOlB,EAAK,MACb,UAAA,GACA,MAAM,yCAERc,EAMWK,EAAA,CALT,KAAK,SACJ,QAASpB,EAAO,MACjB,MAAM,QAAA,aACP,IAEDiB,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAFC,SAED,CAAA"}