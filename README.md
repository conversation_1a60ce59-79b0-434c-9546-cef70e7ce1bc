# Prefect Architecture - Simplified

You write flow code → Register with Prefect API → A Worker picks it up → Executes your flow

# Note

If a worker doesn't pick up a flow, the flow just waits in the Scheduled state

No execution happens until a worker comes online and listens to the correct pool.

You should monitor the work pool, worker logs, and flow run status to resolve it.

# Commands

prefect concurrency-limit create sequential 1
prefect concurrency-limit create parallel 100
