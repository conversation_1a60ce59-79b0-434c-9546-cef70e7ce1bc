import{d as A,V as y,u as b,W as h,J as w,h as g,i as k,c as v,o as C,j as a,k as u,m as e,z as V,G as B,H as m,B as j,K as d,L as E}from"./index-g6FNXcTE.js";import{_ as G}from"./AutomationWizard.vue_vue_type_script_setup_true_lang-DFpkKLMX.js";import{u as I}from"./usePageTitle-DEr56mEx.js";import{u as J}from"./usePrefectApi-iH-jzNq9.js";import"./mapper-3GnQ1V33.js";import"./api-CQZ_ymP4.js";const z=A({__name:"AutomationEdit",async setup(N){let o,r;const i=J(),c=y(),l=b(),p=h("automationId"),s=([o,r]=w(()=>i.automations.getAutomation(p.value)),o=await o,r(),o);I(`Edit Automation: ${s.name}`);const f=g(()=>[{text:"Automations",to:c.automations()},{text:s.name}]);async function x(_){try{await i.automations.updateAutomation(p.value,_),d(m.success.automationUpdate),l.push(c.automations())}catch(t){console.error(t);const n=E(t,m.error.automationUpdate);d(n,"error",{timeout:!1})}}return(_,t)=>{const n=k("p-layout-default");return C(),v(n,{class:"workspace-automation-create"},{header:a(()=>[u(e(V),{crumbs:f.value},{actions:a(()=>[u(e(B),{to:e(m).docs.automations},{default:a(()=>t[0]||(t[0]=[j(" Documentation ")])),_:1,__:[0]},8,["to"])]),_:1},8,["crumbs"])]),default:a(()=>[u(G,{automation:e(s),editing:"",onSubmit:x},null,8,["automation"])]),_:1})}}});export{z as default};
//# sourceMappingURL=AutomationEdit-OF1uy6ny.js.map
