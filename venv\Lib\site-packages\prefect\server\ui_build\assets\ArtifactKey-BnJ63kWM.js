import{d as m,f as y,W as _,g as k,h as s,H as o,i as d,c as e,o as t,j as n,a as r,q as v,m as c,aH as C,F as g,aN as h,aO as x}from"./index-g6FNXcTE.js";import{u as B}from"./usePageTitle-DEr56mEx.js";const N=m({__name:"ArtifactKey",setup(F){const u=y(),i=_("artifactKey"),l=k(u.artifacts.getArtifactCollection,[i]),a=s(()=>l.response),f=s(()=>a.value?`${o.info.artifact}: ${a.value.key}`:o.info.artifact);return B(f),(K,w)=>{const p=d("p-layout-default");return t(),e(p,{class:"artifact"},{header:n(()=>[a.value?(t(),e(c(x),{key:0,artifact:a.value},null,8,["artifact"])):r("",!0)]),default:n(()=>[a.value?(t(),e(c(C),{key:0,artifact:a.value},null,8,["artifact"])):r("",!0),a.value?(t(),v(g,{key:1},[a.value.key?(t(),e(c(h),{key:0,"artifact-key":a.value.key},null,8,["artifact-key"])):r("",!0)],64)):r("",!0)]),_:1})}}});export{N as default};
//# sourceMappingURL=ArtifactKey-BnJ63kWM.js.map
