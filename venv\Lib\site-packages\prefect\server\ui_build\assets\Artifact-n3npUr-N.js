import{d as T,f as h,W as H,g as I,h as p,ae as N,al as S,H as d,aG as V,i as n,c,o as l,j as e,q as $,a as i,k as s,m as t,aH as D,aI as L,aJ as _,B as U,t as W,aK as v,a6 as j,aL as m,aM as q}from"./index-g6FNXcTE.js";import{u as E}from"./usePageTitle-DEr56mEx.js";const F={key:0},P=T({__name:"Artifact",setup(G){const b=h(),k=H("artifactId"),w=I(b.artifacts.getArtifact,[k]),a=p(()=>w.response),r=N(!1),y=[{label:"Artifact"},{label:"Details"},{label:"Raw"}],u=S("tab","Artifact"),C=p(()=>a.value?`${d.info.artifact}: ${a.value.key??V(a.value.type)}`:d.info.artifact);return E(C),(J,o)=>{const x=n("p-divider"),g=n("p-button"),A=n("p-content"),B=n("p-tabs"),R=n("p-layout-well");return l(),c(R,{class:"artifact"},{header:e(()=>[a.value?(l(),c(t(q),{key:0,artifact:a.value},null,8,["artifact"])):i("",!0)]),well:e(()=>[a.value?(l(),c(t(m),{key:0,artifact:a.value,alternate:""},null,8,["artifact"])):i("",!0)]),default:e(()=>[a.value?(l(),$("section",F,[s(t(D),{artifact:a.value},null,8,["artifact"]),s(x),t(L).xl?(l(),c(A,{key:0},{default:e(()=>[s(t(_),{artifact:a.value},null,8,["artifact"]),s(g,{class:"artifact__raw-data-button",small:"",onClick:o[0]||(o[0]=f=>r.value=!r.value)},{default:e(()=>[U(W(r.value?"Hide":"Show")+" raw data ",1)]),_:1}),r.value?(l(),c(t(v),{key:0,artifact:a.value},null,8,["artifact"])):i("",!0)]),_:1})):(l(),c(B,{key:1,selected:t(u),"onUpdate:selected":o[1]||(o[1]=f=>j(u)?u.value=f:null),tabs:y},{artifact:e(()=>[s(t(_),{artifact:a.value},null,8,["artifact"])]),details:e(()=>[s(t(m),{artifact:a.value},null,8,["artifact"])]),raw:e(()=>[s(t(v),{artifact:a.value},null,8,["artifact"])]),_:1},8,["selected"]))])):i("",!0)]),_:1})}}});export{P as default};
//# sourceMappingURL=Artifact-n3npUr-N.js.map
