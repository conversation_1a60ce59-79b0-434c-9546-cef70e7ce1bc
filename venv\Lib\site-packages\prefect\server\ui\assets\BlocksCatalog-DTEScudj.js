import{d as b,f,ae as m,h as o,ci as y,g as d,i as _,c as C,o as g,j as s,k as t,m as l,cj as v,ck as B}from"./index-g6FNXcTE.js";import{u as T}from"./usePageTitle-DEr56mEx.js";const S=b({__name:"BlocksCatalog",setup(h){const c=f(),a=m(null),p=o(()=>a.value?[a.value]:[]),{filter:n}=y({blockSchemas:{blockCapabilities:p}}),i=d(c.blockTypes.getBlockTypes,[n]),u=o(()=>i.response??[]);return T("Blocks Catalog"),(x,e)=>{const r=_("p-layout-default");return g(),C(r,{class:"blocks-catalog"},{header:s(()=>[t(l(B))]),default:s(()=>[t(l(v),{capability:a.value,"onUpdate:capability":e[0]||(e[0]=k=>a.value=k),"block-types":u.value},null,8,["capability","block-types"])]),_:1})}}});export{S as default};
//# sourceMappingURL=BlocksCatalog-DTEScudj.js.map
