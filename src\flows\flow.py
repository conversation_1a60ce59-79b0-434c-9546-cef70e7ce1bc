from prefect import flow
from ..tasks.task import ShopifyTask

class ShopifyFlow:

    def __init__(self):
        self.shopify_task = ShopifyTask()

    @flow(name="fetch_and_load_shopify_data")
    def fetch_and_load_shopify_data(self):
        start_date = "2023-01-01"
        end_date = "2023-01-01"
        shop_name = "alayathelabel"
        access_token = "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"
        self.shopify_task.get_shopify_data(shop_name, access_token, start_date, end_date)