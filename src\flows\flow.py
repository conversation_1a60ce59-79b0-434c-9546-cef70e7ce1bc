from prefect import flow
from prefect.deployments import run_deployment
import aiohttp
from src.tasks.task import ShopifyTask

class ShopifyFlow:

    def __init__(self):
        self.shopify_task = ShopifyTask()


    async def fetch_shopify_data(self):
        start_date = "2023-01-01"
        end_date = "2023-01-01"
        shop_name = "alayathelabel"
        access_token = "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"
        try:
            get_shopify_data_future = self.shopify_task.get_shopify_data.with_options(
                name="get_shopify_data"
            ).submit(shop_name, access_token, start_date, end_date)
            shopify_data = get_shopify_data_future.result()
            invoke_load_shopify_data_future = self.shopify_task.invoke_load_shopify_data.with_options(
                name="invoke_load_shopify_data",
                retry_delay_seconds=30,
                retries=3,
            ).submit(shopify_data)
            invoke_load_shopify_data_future.wait()
        except Exception as e:
            print(f"Error getting shopify data: {e}")
            raise e
        
    async def load_shopify_data(self, shopify_data):
        load_shopify_data_future = self.shopify_task.load_shopify_data.with_options(
            name="load_shopify_data",
            retry_delay_seconds=300,
            retries=3,
        ).submit(shopify_data)
        load_shopify_data_future.wait()

@flow(name="fetch_shopify_data")
async def fetch_shopify_data():
    shopify_flow = ShopifyFlow()
    await shopify_flow.fetch_shopify_data()

@flow(name="load_shopify_data")
async def load_shopify_data(shopify_data):
    shopify_flow = ShopifyFlow()
    await shopify_flow.load_shopify_data(shopify_data)