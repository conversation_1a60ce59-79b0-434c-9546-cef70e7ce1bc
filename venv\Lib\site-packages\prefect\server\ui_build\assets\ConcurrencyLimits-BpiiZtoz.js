import{d as f,cz as b,al as w,i as u,c as y,o as M,j as a,k as l,a6 as r,m as o,z as i,cA as h,cB as x,cC as G,cD as g}from"./index-g6FNXcTE.js";const T=f({__name:"ConcurrencyLimits",setup(R){const{showModal:t,open:d}=b(),{showModal:e,open:m}=b(),_=[{label:"Global"},{label:"Task Run"}],c=w("tab","Global");return(v,s)=>{const p=u("p-button"),k=u("p-tabs"),C=u("p-layout-default");return M(),y(C,{class:"concurrency-limits"},{header:a(()=>[l(o(i),{crumbs:[{text:"Concurrency"}]})]),default:a(()=>[l(k,{selected:o(c),"onUpdate:selected":s[2]||(s[2]=n=>r(c)?c.value=n:null),tabs:_},{global:a(()=>[l(o(i),{size:"lg",crumbs:[{text:"Global Concurrency Limits"}]},{"after-crumbs":a(()=>[l(p,{small:"",icon:"PlusIcon",onClick:o(d)},null,8,["onClick"])]),_:1}),l(o(G),{showModal:o(t),"onUpdate:showModal":s[0]||(s[0]=n=>r(t)?t.value=n:null)},null,8,["showModal"]),l(o(g),{class:"concurrency-limits__global-table"})]),"task-run":a(()=>[l(o(i),{size:"lg",crumbs:[{text:"Task Run Concurrency Limits"}]},{"after-crumbs":a(()=>[l(p,{small:"",icon:"PlusIcon",onClick:o(m)},null,8,["onClick"])]),_:1}),l(o(h),{showModal:o(e),"onUpdate:showModal":s[1]||(s[1]=n=>r(e)?e.value=n:null)},null,8,["showModal"]),l(o(x),{class:"concurrency-limits__task-limits-table"})]),_:1},8,["selected"])]),_:1})}}});export{T as default};
//# sourceMappingURL=ConcurrencyLimits-BpiiZtoz.js.map
