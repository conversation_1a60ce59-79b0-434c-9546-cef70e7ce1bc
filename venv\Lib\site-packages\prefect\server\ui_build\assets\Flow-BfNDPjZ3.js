import{d as R,bP as C,h as t,aR as B,aS as D,e as b,q as P,o as a,l as T,k as n,m as e,bQ as V,aW as N,f as W,W as E,u as U,al as $,g as j,bu as q,bR as z,i as F,c,j as o,a as d,a6 as G,bz as H,bS as M,bT as O,bU as Q,be as Z}from"./index-g6FNXcTE.js";import{s as A}from"./index-B4HswuBc.js";import{u as J}from"./usePageTitle-DEr56mEx.js";const K={class:"flow-stats"},L={class:"flow-stats__cards"},X=R({__name:"FlowStats",props:{flowId:{}},setup(w){const f=w,r={range:{type:"span",seconds:-604800}},{flowId:u}=C(f),i=t(()=>({flowId:u.value,range:r.range}));B(D,{interval:A(30)});const l=t(()=>b.map("FlowStatsFilter",i.value,"FlowRunsFilter")),p=t(()=>b.map("FlowStatsFilter",i.value,"TaskRunsFilter"));return(_,m)=>(a(),P("div",K,[T("div",L,[n(e(V),{filter:l.value},null,8,["filter"]),n(e(N),{filter:p.value},null,8,["filter"])])]))}}),le=R({__name:"Flow",setup(w){const f=W(),r=E("flowId"),u=t(()=>[r.value]),i=U(),l=$("tab","Runs"),p=["Runs","Deployments","Details"],_={interval:3e5},m=j(f.flows.getFlow,[r.value],_),s=t(()=>m.response),{filter:y}=q({flows:{id:u}}),{filter:k}=z({flows:{id:u}});function S(){i.push(Z.flows())}const g=t(()=>s.value?`Flow: ${s.value.name}`:"Flow");return J(g),(Y,v)=>{const x=F("p-tabs"),h=F("p-layout-default");return a(),c(h,{class:"flow"},{header:o(()=>[s.value?(a(),c(e(Q),{key:0,flow:s.value,onDelete:S},null,8,["flow"])):d("",!0)]),default:o(()=>[s.value?(a(),c(X,{key:0,"flow-id":s.value.id},null,8,["flow-id"])):d("",!0),n(x,{selected:e(l),"onUpdate:selected":v[0]||(v[0]=I=>G(l)?l.value=I:null),tabs:p},{details:o(()=>[s.value?(a(),c(e(O),{key:0,flow:s.value},null,8,["flow"])):d("",!0)]),deployments:o(()=>[n(e(M),{filter:e(k),prefix:"deployments"},null,8,["filter"])]),runs:o(()=>[n(e(H),{filter:e(y),selectable:"",prefix:"runs"},null,8,["filter"])]),_:1},8,["selected"])]),_:1})}}});export{le as default};
//# sourceMappingURL=Flow-BfNDPjZ3.js.map
