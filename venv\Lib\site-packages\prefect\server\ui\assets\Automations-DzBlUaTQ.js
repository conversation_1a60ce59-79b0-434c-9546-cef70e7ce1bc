import{d as b,V as A,i as r,c,o as s,j as a,k as t,l as m,q as d,a as C,m as e,B as h,t as v,M as T,A as U,n as $,C as q,x as z,y as E,F as k,g as F,h as y,D as G,Y as M,z as S,E as Y,G as j,H as w}from"./index-g6FNXcTE.js";import{u as H}from"./usePageTitle-DEr56mEx.js";import{u as I}from"./usePrefectApi-iH-jzNq9.js";import"./api-CQZ_ymP4.js";import"./mapper-3GnQ1V33.js";const L={class:"automation-card__header"},J={class:"automation-card__header-actions"},K={key:0,class:"automation-card__description"},O={class:"automation-card__label"},Q=b({__name:"AutomationCard",props:{automation:{}},emits:["update"],setup(V,{emit:p}){const l=p,_=A();return(o,n)=>{const f=r("p-link"),u=r("p-content"),g=r("p-card");return s(),c(g,{class:"automation-card"},{default:a(()=>[t(u,null,{default:a(()=>[t(u,{secondary:""},{default:a(()=>[m("div",L,[t(f,{class:"automation-card__name",to:e(_).automation(o.automation.id)},{default:a(()=>[h(v(o.automation.name),1)]),_:1},8,["to"]),m("div",J,[t(e(T),{automation:o.automation,onUpdate:n[0]||(n[0]=i=>l("update"))},null,8,["automation"]),t(e(U),{automation:o.automation,onDelete:n[1]||(n[1]=i=>l("update"))},null,8,["automation"])])]),o.automation.description?(s(),d("p",K,v(o.automation.description),1)):C("",!0)]),_:1}),t(u,{secondary:""},{default:a(()=>[n[2]||(n[2]=m("span",{class:"automation-card__label"},"Trigger",-1)),t(e($),{trigger:o.automation.trigger},null,8,["trigger"])]),_:1,__:[2]}),t(u,{secondary:""},{default:a(()=>[m("span",O,v(e(q)("Action",o.automation.actions.length)),1),(s(!0),d(k,null,z(o.automation.actions,i=>(s(),c(g,{key:i.id},{default:a(()=>[t(e(E),{action:i},null,8,["action"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})}}}),tt=b({__name:"Automations",setup(V){const p=A();H("Automations");const l=[{text:"Automations"}],_=I(),o=F(_.automations.getAutomations),n=y(()=>o.response??[]),f=y(()=>o.executed),u=y(()=>n.value.length===0);return(g,i)=>{const B=r("p-button"),D=r("p-virtual-scroller"),N=r("p-layout-default");return s(),c(N,{class:"automations"},{header:a(()=>[t(e(S),{crumbs:l},Y({"after-crumbs":a(()=>[t(B,{size:"sm",icon:"PlusIcon",to:e(p).automationCreate()},null,8,["to"])]),_:2},[u.value?void 0:{name:"actions",fn:a(()=>[t(e(j),{to:e(w).docs.automations},{default:a(()=>i[0]||(i[0]=[h(" Documentation ")])),_:1,__:[0]},8,["to"])]),key:"0"}]),1024)]),default:a(()=>[f.value?(s(),d(k,{key:0},[u.value?(s(),c(e(G),{key:0})):(s(),d(k,{key:1},[t(e(M),{count:n.value.length,label:"automation"},null,8,["count"]),t(D,{items:n.value,class:"automations-list"},{default:a(({item:P})=>[t(Q,{automation:P,onUpdate:e(o).refresh},null,8,["automation","onUpdate"])]),_:1},8,["items"])],64))],64)):C("",!0)]),_:1})}}});export{tt as default};
//# sourceMappingURL=Automations-DzBlUaTQ.js.map
