{"version": 3, "file": "WorkPoolQueue-BQ8TQCPW.js", "sources": ["../../src/pages/WorkPoolQueue.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"workPoolQueue\" class=\"work-pool-queue\">\n    <template #header>\n      <PageHeadingWorkPoolQueue :work-pool-queue=\"workPoolQueue\" :work-pool-name=\"workPoolName\" @update=\"workPoolQueuesSubscription.refresh\" />\n    </template>\n\n    <p-layout-well class=\"work-pool-queue__body\">\n      <template #header>\n        <CodeBanner :command=\"codeBannerCliCommand\" :title=\"codeBannerTitle\" :subtitle=\"codeBannerSubtitle\" />\n      </template>\n\n      <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n        <template #details>\n          <WorkPoolQueueDetails :work-pool-name=\"workPoolName\" :work-pool-queue=\"workPoolQueue\" />\n        </template>\n\n        <template #upcoming-runs>\n          <WorkPoolQueueUpcomingFlowRunsList :work-pool-name=\"workPoolName\" :work-pool-queue=\"workPoolQueue\" />\n        </template>\n\n        <template #runs>\n          <FlowRunFilteredList :filter=\"flowRunFilter\" prefix=\"runs\" />\n        </template>\n      </p-tabs>\n\n      <template #well>\n        <WorkPoolQueueDetails alternate :work-pool-name=\"workPoolName\" :work-pool-queue=\"workPoolQueue\" />\n      </template>\n    </p-layout-well>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { media } from '@prefecthq/prefect-design'\n  import { useWorkspaceApi, PageHeadingWorkPoolQueue, CodeBanner, WorkPoolQueueDetails, WorkPoolQueueUpcomingFlowRunsList, FlowRunFilteredList, useFlowRunsFilter, useTabs } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam, useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const workPoolName = useRouteParam('workPoolName')\n  const workPoolNames = computed(() => [workPoolName.value])\n  const workPoolQueueName = useRouteParam('workPoolQueueName')\n  const workPoolQueueNames = computed(() => [workPoolQueueName.value])\n  const subscriptionOptions = {\n    interval: 300000,\n  }\n\n  const workPoolQueuesSubscription = useSubscription(api.workPoolQueues.getWorkPoolQueueByName, [workPoolName.value, workPoolQueueName.value], subscriptionOptions)\n  const workPoolQueue = computed(() => workPoolQueuesSubscription.response)\n\n  const workPoolSubscription = useSubscription(api.workPools.getWorkPoolByName, [workPoolName.value], subscriptionOptions)\n  const workPool = computed(() => workPoolSubscription.response)\n  const isAgentWorkPool = computed(() => workPool.value?.type === 'prefect-agent')\n\n  const codeBannerTitle = computed(() => {\n    if (!workPoolQueue.value) {\n      return 'Your work queue is ready to go!'\n    }\n    return `Your work pool ${workPoolQueue.value.name} is ready to go!`\n  })\n  const codeBannerCliCommand = computed(() => `prefect ${isAgentWorkPool.value ? 'agent' : 'worker'} start --pool \"${workPoolName.value}\" --work-queue \"${workPoolQueueName.value}\"`)\n  const codeBannerSubtitle = computed(() => `Work queues are scoped to a work pool to allow ${isAgentWorkPool.value ? 'agents' : 'workers'} to pull from groups of queues with different priorities.`)\n\n  const { filter: flowRunFilter } = useFlowRunsFilter({\n    workPoolQueues: {\n      name: workPoolQueueNames,\n    },\n    workPools: {\n      name: workPoolNames,\n    },\n  })\n\n  const computedTabs = computed(() => [\n    { label: 'Details', hidden: media.xl },\n    { label: 'Upcoming Runs' },\n    { label: 'Runs' },\n  ])\n\n  const tab = useRouteQueryParam('tab', 'Details')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  const title = computed(() => {\n    if (!workPoolQueueName.value) {\n      return 'Work Pool Queue'\n    }\n    return `Work Pool Queue: ${workPoolQueueName.value}`\n  })\n\n  usePageTitle(title)\n</script>\n\n<style>\n/* This is an override since this is using nested layouts */\n.work-pool-queue__body {\n  @apply\n  p-0\n}\n</style>"], "names": ["api", "useWorkspaceApi", "workPoolName", "useRouteParam", "workPoolNames", "computed", "workPoolQueueName", "workPoolQueueNames", "subscriptionOptions", "workPoolQueuesSubscription", "useSubscription", "workPoolQueue", "workPoolSubscription", "workPool", "isAgentWorkPool", "_a", "codeBannerTitle", "codeBannerCliCommand", "codeBannerSubtitle", "flowRunFilter", "useFlowRunsFilter", "computedTabs", "media", "tab", "useRouteQueryParam", "tabs", "useTabs", "title", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingWorkPoolQueue", "_component_p_layout_well", "CodeBanner", "WorkPoolQueueDetails", "_component_p_tabs", "$event", "WorkPoolQueueUpcomingFlowRunsList", "FlowRunFilteredList"], "mappings": "+RAuCE,MAAMA,EAAMC,EAAgB,EACtBC,EAAeC,EAAc,cAAc,EAC3CC,EAAgBC,EAAS,IAAM,CAACH,EAAa,KAAK,CAAC,EACnDI,EAAoBH,EAAc,mBAAmB,EACrDI,EAAqBF,EAAS,IAAM,CAACC,EAAkB,KAAK,CAAC,EAC7DE,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAA6BC,EAAgBV,EAAI,eAAe,uBAAwB,CAACE,EAAa,MAAOI,EAAkB,KAAK,EAAGE,CAAmB,EAC1JG,EAAgBN,EAAS,IAAMI,EAA2B,QAAQ,EAElEG,EAAuBF,EAAgBV,EAAI,UAAU,kBAAmB,CAACE,EAAa,KAAK,EAAGM,CAAmB,EACjHK,EAAWR,EAAS,IAAMO,EAAqB,QAAQ,EACvDE,EAAkBT,EAAS,IAAA,OAAM,QAAAU,EAAAF,EAAS,QAAT,YAAAE,EAAgB,QAAS,gBAAe,EAEzEC,EAAkBX,EAAS,IAC1BM,EAAc,MAGZ,kBAAkBA,EAAc,MAAM,IAAI,mBAFxC,iCAGV,EACKM,EAAuBZ,EAAS,IAAM,WAAWS,EAAgB,MAAQ,QAAU,QAAQ,kBAAkBZ,EAAa,KAAK,mBAAmBI,EAAkB,KAAK,GAAG,EAC5KY,EAAqBb,EAAS,IAAM,kDAAkDS,EAAgB,MAAQ,SAAW,SAAS,2DAA2D,EAE7L,CAAE,OAAQK,CAAc,EAAIC,EAAkB,CAClD,eAAgB,CACd,KAAMb,CACR,EACA,UAAW,CACT,KAAMH,CAAA,CACR,CACD,EAEKiB,EAAehB,EAAS,IAAM,CAClC,CAAE,MAAO,UAAW,OAAQiB,EAAM,EAAG,EACrC,CAAE,MAAO,eAAgB,EACzB,CAAE,MAAO,MAAO,CAAA,CACjB,EAEKC,EAAMC,EAAmB,MAAO,SAAS,EACzC,CAAE,KAAAC,CAAS,EAAAC,EAAQL,EAAcE,CAAG,EAEpCI,EAAQtB,EAAS,IAChBC,EAAkB,MAGhB,oBAAoBA,EAAkB,KAAK,GAFzC,iBAGV,EAED,OAAAsB,EAAaD,CAAK,kFAxFMhB,EAAa,WAArCkB,EA4BmBC,EAAA,OA5BoB,MAAM,iBAAA,GAChC,SACT,IAAyI,CAAzIC,EAAyIC,EAAAC,CAAA,EAAA,CAA9G,kBAAiBtB,EAAa,MAAG,iBAAgBqB,EAAY9B,CAAA,EAAG,SAAQ8B,EAA0BvB,CAAA,EAAC,6EAGhI,IAsBgB,CAtBhBsB,EAsBgBG,EAAA,CAtBD,MAAM,yBAAuB,CAC/B,SACT,IAAsG,CAAtGH,EAAsGC,EAAAG,CAAA,EAAA,CAAzF,QAASlB,EAAoB,MAAG,MAAOD,EAAe,MAAG,SAAUE,EAAkB,gDAiBzF,OACT,IAAkG,CAAlGa,EAAkGC,EAAAI,CAAA,EAAA,CAA5E,UAAA,GAAW,iBAAgBJ,EAAY9B,CAAA,EAAG,kBAAiBS,EAAa,gEAfhG,IAYS,CAZToB,EAYSM,EAAA,CAZO,SAAUL,EAAGT,CAAA,0CAAHA,EAAG,MAAAe,EAAA,MAAG,KAAMN,EAAIP,CAAA,CAAA,GAC7B,UACT,IAAwF,CAAxFM,EAAwFC,EAAAI,CAAA,EAAA,CAAjE,iBAAgBJ,EAAY9B,CAAA,EAAG,kBAAiBS,EAAa,sDAG3E,kBACT,IAAqG,CAArGoB,EAAqGC,EAAAO,CAAA,EAAA,CAAjE,iBAAgBP,EAAY9B,CAAA,EAAG,kBAAiBS,EAAa,sDAGxF,OACT,IAA6D,CAA7DoB,EAA6DC,EAAAQ,CAAA,EAAA,CAAvC,OAAQR,EAAab,CAAA,EAAE,OAAO,MAAA"}