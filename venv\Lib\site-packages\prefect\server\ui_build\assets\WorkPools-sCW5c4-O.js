import{d as m,f as i,g as k,h as t,i as _,c as a,o,j as n,q as f,a as P,F as h,m as s,cY as w,cZ as y,k as g,c_ as v}from"./index-g6FNXcTE.js";import{u as x}from"./usePageTitle-DEr56mEx.js";const I=m({__name:"WorkPools",setup(C){const r=i(),c={interval:3e4},e=k(r.workPools.getWorkPools,[{}],c),l=t(()=>e.response??[]),p=t(()=>e.executed&&l.value.length==0),u=t(()=>e.executed);return x("Work Pools"),(B,W)=>{const d=_("p-layout-default");return o(),a(d,{class:"work-pools"},{header:n(()=>[g(s(v))]),default:n(()=>[u.value?(o(),f(h,{key:0},[p.value?(o(),a(s(w),{key:0})):(o(),a(s(y),{key:1,onUpdate:s(e).refresh},null,8,["onUpdate"]))],64)):P("",!0)]),_:1})}}});export{I as default};
//# sourceMappingURL=WorkPools-sCW5c4-O.js.map
