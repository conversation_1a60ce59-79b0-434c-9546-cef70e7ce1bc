{"version": 3, "file": "FlowRun-T0ma01v4.js", "sources": ["../../src/components/FlowRunGraphs.vue", "../../src/pages/FlowRun.vue"], "sourcesContent": ["<template>\n  <div class=\"flow-run-graphs\" :class=\"classes.root\">\n    <div class=\"flow-run-graphs__graph-panel-container\">\n      <div class=\"flow-run-graphs__graphs\">\n        <FlowRunGraph\n          v-model:fullscreen=\"fullscreen\"\n          v-model:viewport=\"dateRange\"\n          v-model:selected=\"selection\"\n          :flow-run\n          :fetch-events\n          class=\"flow-run-graphs__flow-run\"\n        />\n      </div>\n      <div class=\"flow-run-graphs__panel p-background\">\n        <FlowRunGraphSelectionPanel\n          v-if=\"selection?.kind === 'task-run' || selection?.kind === 'flow-run'\"\n          v-model:selection=\"selection\"\n          :floating=\"fullscreen\"\n        />\n      </div>\n    </div>\n    <FlowRunGraphEventPopover\n      v-if=\"selection && selection.kind === 'event'\"\n      v-model:selection=\"selection\"\n    />\n    <FlowRunGraphEventsPopover\n      v-if=\"selection && selection.kind === 'events'\"\n      v-model:selection=\"selection\"\n    />\n    <FlowRunGraphArtifactsPopover\n      v-if=\"selection && selection.kind === 'artifacts'\"\n      v-model:selection=\"selection\"\n    />\n    <FlowRunGraphStatePopover\n      v-if=\"selection?.kind === 'state'\"\n      v-model:selection=\"selection\"\n    />\n    <FlowRunGraphArtifactDrawer v-model:selection=\"selection\" />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\n  import {\n    FlowRunGraph,\n    RunGraphItemSelection,\n    RunGraphViewportDateRange,\n    FlowRun,\n    FlowRunGraphSelectionPanel,\n    FlowRunGraphArtifactDrawer,\n    FlowRunGraphArtifactsPopover,\n    FlowRunGraphStatePopover,\n    RunGraphFetchEventsContext,\n    FlowRunGraphEventPopover,\n    FlowRunGraphEventsPopover,\n    RunGraphEvent,\n    WorkspaceEventsFilter,\n    useWorkspaceApi\n  } from '@prefecthq/prefect-ui-library'\n  import { computed, ref } from 'vue'\n\n  defineProps<{\n    flowRun: FlowRun,\n  }>()\n\n  const api = useWorkspaceApi()\n  const dateRange = ref<RunGraphViewportDateRange>()\n\n  const fullscreen = ref(false)\n  const selection = ref<RunGraphItemSelection | null>(null)\n\n  const classes = computed(() => {\n    return {\n      root: {\n        'flow-run-graphs--fullscreen': fullscreen.value,\n        'flow-run-graphs--show-panel': Boolean(\n          selection.value?.kind === 'task-run'\n            || selection.value?.kind === 'flow-run',\n        ),\n      },\n    }\n  })\n\n  const fetchEvents = async ({ nodeId, since, until }: RunGraphFetchEventsContext): Promise<RunGraphEvent[]> => {\n    const filter: WorkspaceEventsFilter = {\n      anyResource: {\n        id: [`prefect.flow-run.${nodeId}`],\n      },\n      event: {\n        excludePrefix: ['prefect.log.write', 'prefect.task-run.'],\n      },\n      occurred: {\n        since,\n        until,\n      },\n    }\n\n    const { events } = await api.events.getEvents(filter)\n\n    return events\n  }\n</script>\n\n<style>\n.flow-run-graphs { @apply\n  relative;\n  --flow-run-graphs-panel-width: 320px;\n}\n\n.flow-run-graphs__graph-panel-container { @apply\n  relative\n  grid\n  grid-cols-1\n  gap-2\n  overflow-hidden\n}\n\n.flow-run-graphs--fullscreen { @apply\n  z-20\n  static\n}\n\n.flow-run-graphs__graphs { @apply\n  transition-[width];\n  width: 100%;\n}\n\n.flow-run-graphs--show-panel .flow-run-graphs__graphs {\n  width: calc(100% - var(--flow-run-graphs-panel-width) - theme(spacing.2));\n}\n\n.flow-run-graphs__flow-run { @apply\n  overflow-hidden\n  rounded\n}\n\n.flow-run-graphs__panel { @apply\n  absolute\n  right-0\n  top-0\n  bottom-0\n  translate-x-full\n  transition-transform\n  rounded;\n  width: var(--flow-run-graphs-panel-width)\n}\n\n.flow-run-graphs--fullscreen .flow-run-graphs__panel { @apply\n  bg-floating\n  top-4\n  right-4\n  bottom-auto\n}\n\n.flow-run-graphs--show-panel .flow-run-graphs__panel { @apply\n  translate-x-0\n}\n</style>\n", "<template>\n  <p-layout-default v-if=\"flowRun\" :key=\"flowRun.id\" class=\"flow-run\">\n    <template #header>\n      <PageHeadingFlowRun :flow-run-id=\"flowRun.id\" @delete=\"goToRuns\" />\n    </template>\n\n    <FlowRunGraphs v-if=\"!isPending\" :flow-run=\"flowRun\" />\n\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #details>\n        <FlowRunDetails :flow-run=\"flowRun\" />\n      </template>\n\n      <template #logs>\n        <FlowRunLogs :flow-run=\"flowRun\" />\n      </template>\n\n      <template #artifacts>\n        <FlowRunArtifacts :flow-run=\"flowRun\" />\n      </template>\n\n      <template #task-runs>\n        <FlowRunTaskRuns :flow-run-id=\"flowRun.id\" />\n      </template>\n\n      <template #subflow-runs>\n        <FlowRunFilteredList :filter=\"subflowsFilter\" />\n      </template>\n\n      <template #parameters>\n        <CopyableWrapper :text-to-copy=\"parameters\">\n          <p-code-highlight lang=\"json\" :text=\"parameters\" class=\"flow-run__parameters\" />\n        </CopyableWrapper>\n      </template>\n\n      <template #job-variables>\n        <CopyableWrapper :text-to-copy=\"jobVariables\">\n          <p-code-highlight lang=\"json\" :text=\"jobVariables\" class=\"flow-run__job-variables\" />\n        </CopyableWrapper>\n      </template>\n    </p-tabs>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import {\n    PageHeadingFlowRun,\n    FlowRunArtifacts,\n    FlowRunDetails,\n    FlowRunLogs,\n    FlowRunTaskRuns,\n    FlowRunFilteredList,\n    useFlowRunFavicon,\n    CopyableWrapper,\n    isPendingStateType,\n    useTabs,\n    httpStatus,\n    useFlowRun,\n    useFlowRunsFilter,\n    stringify\n  } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam } from '@prefecthq/vue-compositions'\n  import { computed, watchEffect } from 'vue'\n  import { useRouter } from 'vue-router'\n  import FlowRunGraphs from '@/components/FlowRunGraphs.vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n\n\n  const router = useRouter()\n  const flowRunId = useRouteParam('flowRunId')\n\n  const { flowRun, subscription: flowRunSubscription } = useFlowRun(flowRunId, { interval: 5000 })\n  const parameters = computed(() => stringify(flowRun.value?.parameters ?? {}))\n\n  const isPending = computed(() => {\n    return flowRun.value?.stateType ? isPendingStateType(flowRun.value.stateType) : true\n  })\n\n  const jobVariables = computed(() => stringify(flowRun.value?.jobVariables ?? {}))\n\n  const computedTabs = computed(() => [\n    { label: 'Logs' },\n    { label: 'Task Runs', hidden: isPending.value },\n    { label: 'Subflow Runs', hidden: isPending.value },\n    { label: 'Artifacts', hidden: isPending.value },\n    { label: 'Details' },\n    { label: 'Parameters' },\n    { label: 'Job Variables' },\n  ])\n  const tab = useRouteQueryParam('tab', 'Logs')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  const parentFlowRunIds = computed(() => [flowRunId.value])\n  const { filter: subflowsFilter } = useFlowRunsFilter({\n    flowRuns: {\n      parentFlowRunId: parentFlowRunIds,\n    },\n  })\n\n  function goToRuns(): void {\n    router.push(routes.runs())\n  }\n\n  useFlowRunFavicon(flowRun)\n\n  const title = computed(() => {\n    if (!flowRun.value) {\n      return 'Flow Run'\n    }\n    return `Flow Run: ${flowRun.value.name}`\n  })\n  usePageTitle(title)\n\n  watchEffect(() => {\n    if (flowRunSubscription.error) {\n      const status = httpStatus(flowRunSubscription.error)\n\n      if (status.isInRange('clientError')) {\n        router.replace(routes[404]())\n      }\n    }\n  })\n</script>\n\n<style>\n.flow-run { @apply\n  items-start\n}\n\n.flow-run__logs { @apply\n  max-h-screen\n}\n\n.flow-run__header-meta { @apply\n  flex\n  gap-2\n  items-center\n  xl:hidden\n}\n\n.flow-run__job-variables,\n.flow-run__parameters { @apply\n  px-4\n  py-3\n}\n</style>\n"], "names": ["api", "useWorkspaceApi", "date<PERSON><PERSON><PERSON>", "ref", "fullscreen", "selection", "classes", "computed", "_a", "_b", "fetchEvents", "nodeId", "since", "until", "filter", "events", "_createElementBlock", "_normalizeClass", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_unref", "FlowRunGraph", "$event", "flowRun", "_hoisted_3", "_createBlock", "FlowRunGraphSelectionPanel", "FlowRunGraphEventPopover", "FlowRunGraphEventsPopover", "FlowRunGraphArtifactsPopover", "_c", "FlowRunGraphStatePopover", "FlowRunGraphArtifactDrawer", "router", "useRouter", "flowRunId", "useRouteParam", "flowRunSubscription", "useFlowRun", "parameters", "stringify", "isPending", "isPendingStateType", "jobVariables", "computedTabs", "tab", "useRouteQueryParam", "tabs", "useTabs", "parentFlowRunIds", "subflowsFilter", "useFlowRunsFilter", "goToRuns", "routes", "useFlowRunFavicon", "title", "usePageTitle", "watchEffect", "httpStatus", "_component_p_layout_default", "PageHeadingFlowRun", "FlowRunGraphs", "_component_p_tabs", "FlowRunDetails", "FlowRunLogs", "FlowRunArtifacts", "FlowRunTaskRuns", "FlowRunFilteredList", "CopyableWrapper", "_component_p_code_highlight"], "mappings": "4lBAgEE,MAAMA,EAAMC,EAAgB,EACtBC,EAAYC,EAA+B,EAE3CC,EAAaD,EAAI,EAAK,EACtBE,EAAYF,EAAkC,IAAI,EAElDG,EAAUC,EAAS,IAAM,SACtB,MAAA,CACL,KAAM,CACJ,8BAA+BH,EAAW,MAC1C,gCACEI,EAAAH,EAAU,QAAV,YAAAG,EAAiB,QAAS,cACrBC,EAAAJ,EAAU,QAAV,YAAAI,EAAiB,QAAS,UACjC,CAEJ,CAAA,CACD,EAEKC,EAAc,MAAO,CAAE,OAAAC,EAAQ,MAAAC,EAAO,MAAAC,KAAkE,CAC5G,MAAMC,EAAgC,CACpC,YAAa,CACX,GAAI,CAAC,oBAAoBH,CAAM,EAAE,CACnC,EACA,MAAO,CACL,cAAe,CAAC,oBAAqB,mBAAmB,CAC1D,EACA,SAAU,CACR,MAAAC,EACA,MAAAC,CAAA,CAEJ,EAEM,CAAE,OAAAE,CAAO,EAAI,MAAMf,EAAI,OAAO,UAAUc,CAAM,EAE7C,OAAAC,CACT,qCAlGAC,EAqCM,MAAA,CArCD,MAAMC,EAAA,CAAA,kBAA0BX,EAAA,MAAQ,IAAI,CAAA,CAAA,GAC/CY,EAkBM,MAlBNC,GAkBM,CAjBJD,EASM,MATNE,GASM,CARJC,EAOEC,EAAAC,CAAA,EAAA,CANQ,WAAYnB,EAAU,2CAAVA,EAAU,MAAAoB,GACtB,SAAUtB,EAAS,yCAATA,EAAS,MAAAsB,GACnB,SAAUnB,EAAS,yCAATA,EAAS,MAAAmB,GAC1B,WAAAC,EAAQ,QACR,eAAAf,EACD,MAAM,2BAAA,4DAGVQ,EAMM,MANNQ,GAMM,GAJIlB,EAAAH,EAAA,QAAA,YAAAG,EAAW,QAAuB,cAAAC,EAAAJ,EAAA,QAAA,YAAAI,EAAW,QAAI,gBADzDkB,EAIEL,EAAAM,CAAA,EAAA,OAFQ,UAAWvB,EAAS,0CAATA,EAAS,MAAAmB,GAC3B,SAAUpB,EAAU,KAAA,iDAKnBC,EAAS,OAAIA,EAAS,MAAC,OAAI,aADnCsB,EAGEL,EAAAO,CAAA,EAAA,OADQ,UAAWxB,EAAS,0CAATA,EAAS,MAAAmB,EAAA,kCAGtBnB,EAAS,OAAIA,EAAS,MAAC,OAAI,cADnCsB,EAGEL,EAAAQ,CAAA,EAAA,OADQ,UAAWzB,EAAS,0CAATA,EAAS,MAAAmB,EAAA,kCAGtBnB,EAAS,OAAIA,EAAS,MAAC,OAAI,iBADnCsB,EAGEL,EAAAS,CAAA,EAAA,OADQ,UAAW1B,EAAS,0CAATA,EAAS,MAAAmB,EAAA,oCAGtBQ,EAAA3B,EAAA,QAAA,YAAA2B,EAAW,QAAI,aADvBL,EAGEL,EAAAW,CAAA,EAAA,OADQ,UAAW5B,EAAS,0CAATA,EAAS,MAAAmB,EAAA,kCAE9BH,EAA4DC,EAAAY,CAAA,EAAA,CAAxB,UAAW7B,EAAS,0CAATA,EAAS,MAAAmB,EAAA,gECgC1D,MAAMW,EAASC,EAAU,EACnBC,EAAYC,EAAc,WAAW,EAErC,CAAE,QAAAb,EAAS,aAAcc,CAAoB,EAAIC,EAAWH,EAAW,CAAE,SAAU,IAAM,EACzFI,EAAalC,EAAS,WAAMmC,OAAAA,IAAUlC,EAAAiB,EAAQ,QAAR,YAAAjB,EAAe,aAAc,CAAA,CAAE,EAAC,EAEtEmC,EAAYpC,EAAS,IAAM,OAC/B,OAAOC,EAAAiB,EAAQ,QAAR,MAAAjB,EAAe,UAAYoC,EAAmBnB,EAAQ,MAAM,SAAS,EAAI,EAAA,CACjF,EAEKoB,EAAetC,EAAS,WAAMmC,OAAAA,IAAUlC,EAAAiB,EAAQ,QAAR,YAAAjB,EAAe,eAAgB,CAAA,CAAE,EAAC,EAE1EsC,EAAevC,EAAS,IAAM,CAClC,CAAE,MAAO,MAAO,EAChB,CAAE,MAAO,YAAa,OAAQoC,EAAU,KAAM,EAC9C,CAAE,MAAO,eAAgB,OAAQA,EAAU,KAAM,EACjD,CAAE,MAAO,YAAa,OAAQA,EAAU,KAAM,EAC9C,CAAE,MAAO,SAAU,EACnB,CAAE,MAAO,YAAa,EACtB,CAAE,MAAO,eAAgB,CAAA,CAC1B,EACKI,EAAMC,EAAmB,MAAO,MAAM,EACtC,CAAE,KAAAC,CAAS,EAAAC,EAAQJ,EAAcC,CAAG,EAEpCI,EAAmB5C,EAAS,IAAM,CAAC8B,EAAU,KAAK,CAAC,EACnD,CAAE,OAAQe,CAAe,EAAIC,EAAkB,CACnD,SAAU,CACR,gBAAiBF,CAAA,CACnB,CACD,EAED,SAASG,GAAiB,CACjBnB,EAAA,KAAKoB,EAAO,MAAM,CAAA,CAG3BC,EAAkB/B,CAAO,EAEnB,MAAAgC,EAAQlD,EAAS,IAChBkB,EAAQ,MAGN,aAAaA,EAAQ,MAAM,IAAI,GAF7B,UAGV,EACD,OAAAiC,GAAaD,CAAK,EAElBE,EAAY,IAAM,CACZpB,EAAoB,OACPqB,GAAWrB,EAAoB,KAAK,EAExC,UAAU,aAAa,GAChCJ,EAAO,QAAQoB,EAAO,GAAG,EAAA,CAAG,CAEhC,CACD,qFAzHuBjC,EAAOG,CAAA,OAA/BE,EAwCmBkC,EAAA,CAxCe,IAAKvC,EAAOG,CAAA,EAAC,GAAI,MAAM,UAAA,GAC5C,SACT,IAAmE,CAAnEJ,EAAmEC,EAAAwC,EAAA,EAAA,CAA9C,cAAaxC,EAAOG,CAAA,EAAC,GAAK,SAAQ6B,CAAA,sCAGzD,IAAuD,CAAjCX,EAAS,oBAA/BhB,EAAuDoC,GAAA,OAArB,WAAUzC,EAAOG,CAAA,CAAA,wBAEnDJ,EAgCS2C,EAAA,CAhCO,SAAU1C,EAAGyB,CAAA,2CAAHA,EAAG,MAAAvB,EAAA,MAAG,KAAMF,EAAI2B,CAAA,CAAA,GAC7B,UACT,IAAsC,CAAtC5B,EAAsCC,EAAA2C,EAAA,EAAA,CAArB,WAAU3C,EAAOG,CAAA,GAAA,KAAA,EAAA,CAAA,UAAA,CAAA,CAAA,GAGzB,OACT,IAAmC,CAAnCJ,EAAmCC,EAAA4C,EAAA,EAAA,CAArB,WAAU5C,EAAOG,CAAA,GAAA,KAAA,EAAA,CAAA,UAAA,CAAA,CAAA,GAGtB,YACT,IAAwC,CAAxCJ,EAAwCC,EAAA6C,EAAA,EAAA,CAArB,WAAU7C,EAAOG,CAAA,GAAA,KAAA,EAAA,CAAA,UAAA,CAAA,CAAA,GAG3B,cACT,IAA6C,CAA7CJ,EAA6CC,EAAA8C,EAAA,EAAA,CAA3B,cAAa9C,EAAOG,CAAA,EAAC,EAAA,4BAG9B,iBACT,IAAgD,CAAhDJ,EAAgDC,EAAA+C,EAAA,EAAA,CAA1B,OAAQ/C,EAAc8B,CAAA,GAAA,KAAA,EAAA,CAAA,QAAA,CAAA,CAAA,GAGnC,aACT,IAEkB,CAFlB/B,EAEkBC,EAAAgD,CAAA,EAAA,CAFA,eAAc7B,EAAU,OAAA,WACxC,IAAgF,CAAhFpB,EAAgFkD,EAAA,CAA9D,KAAK,OAAQ,KAAM9B,EAAU,MAAE,MAAM,sBAAA,gDAIhD,kBACT,IAEkB,CAFlBpB,EAEkBC,EAAAgD,CAAA,EAAA,CAFA,eAAczB,EAAY,OAAA,WAC1C,IAAqF,CAArFxB,EAAqFkD,EAAA,CAAnE,KAAK,OAAQ,KAAM1B,EAAY,MAAE,MAAM,yBAAA"}