import{d as f,f as C,V as x,W as c,h as r,aC as b,aD as h,J as y,i as D,c as E,a as I,m as e,o as k,j as n,k as s,aE as w,z as F,aF as V}from"./index-g6FNXcTE.js";import{u as g}from"./usePageTitle-DEr56mEx.js";const q=f({__name:"Event",async setup(B){let a,o;const u=C(),v=x(),l=c("eventDate"),_=c("eventId"),m=r(()=>b(l.value)),d=h({startDate:m,eventId:[_.value]}),t=([a,o]=y(()=>u.events.getFirstEvent(d.value)),a=await a,o(),a),p=r(()=>[{text:"Event Feed",to:v.events()},{text:t.eventLabel}]);return g(`Event: ${t.eventLabel}`),(L,N)=>{const i=D("p-layout-default");return e(t)?(k(),E(i,{key:0,class:"event"},{header:n(()=>[s(e(F),{crumbs:p.value},{actions:n(()=>[s(e(V),{event:e(t)},null,8,["event"])]),_:1},8,["crumbs"])]),default:n(()=>[s(e(w),{event:e(t)},null,8,["event"])]),_:1})):I("",!0)}}});export{q as default};
//# sourceMappingURL=Event-hUBWAGlF.js.map
