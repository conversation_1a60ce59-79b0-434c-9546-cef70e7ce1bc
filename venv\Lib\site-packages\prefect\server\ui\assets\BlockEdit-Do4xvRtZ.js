import{d as B,f as D,u as C,W as w,J as x,ae as p,i as g,c as F,a as P,m as e,o as A,j as c,k as s,cn as E,cu as I,Z as J,cp as N,cv as S,K as b,be as T}from"./index-g6FNXcTE.js";import{u as V}from"./usePageTitle-DEr56mEx.js";const K=B({__name:"BlockEdit",async setup(W){let t,n;const l=D(),u=C(),r=w("blockDocumentId"),o=([t,n]=x(()=>l.blockDocuments.getBlockDocument(r.value)),t=await t,n(),t),{blockType:i,blockSchema:_}=o,d=p(o.data),m=p(o.name);function f(k){l.blockDocuments.updateBlockDocument(o.id,k).then(()=>{b("Block updated successfully","success"),u.push(T.block(r.value))}).catch(a=>{b("Failed to update block","error"),console.error(a)})}function y(){u.back()}return V(`Edit Block: ${m.value}`),(k,a)=>{const v=g("p-layout-default");return e(o)?(A(),F(v,{key:0,class:"block-edit"},{header:c(()=>[s(e(S),{"block-document":e(o)},null,8,["block-document"])]),default:c(()=>[s(e(E),{"block-type":e(i)},{default:c(()=>[s(e(I),J({data:d.value,"onUpdate:data":a[0]||(a[0]=h=>d.value=h)},{name:m.value,blockSchema:e(_)},N({submit:f,cancel:y})),null,16,["data"])]),_:1},8,["block-type"])]),_:1})):P("",!0)}}});export{K as default};
//# sourceMappingURL=BlockEdit-Do4xvRtZ.js.map
