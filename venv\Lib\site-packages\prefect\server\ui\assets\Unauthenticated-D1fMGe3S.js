import{d as b,ae as l,u as y,i as n,q as C,o as S,l as c,k as u,j as p,B as d,dr as V,K as m}from"./index-g6FNXcTE.js";import{u as k}from"./usePrefectApi-iH-jzNq9.js";import"./api-CQZ_ymP4.js";import"./mapper-3GnQ1V33.js";const I={class:"flex items-center justify-center min-h-screen"},B={class:"w-full max-w-[400px] p-8 m-4 bg-surface-raised rounded-lg shadow-lg"},U=b({__name:"Unauthenticated",props:{redirect:{}},setup(f){const h=f,o=l(""),t=l(!1),s=l(""),a=y(),i=k(),v=async()=>{if(!t.value){t.value=!0,s.value="";try{localStorage.setItem("prefect-password",btoa(o.value)),i.admin.authCheck().then(r=>{r==401?(localStorage.removeItem("prefect-pasword"),m("Authentication failed.","error",{timeout:!1}),a.currentRoute.value.name!=="login"&&a.push({name:"login",query:{redirect:a.currentRoute.value.fullPath}})):i.health.isHealthy().then(e=>{e||m(`Can't connect to Server API at ${config.baseUrl}. Check that it's accessible from your machine.`,"error",{timeout:!1}),a.push(h.redirect||"/")})})}catch{localStorage.removeItem("prefect-password"),s.value="Invalid password"}finally{t.value=!1}}};return(r,e)=>{const _=n("p-heading"),g=n("p-text-input"),w=n("p-button");return S(),C("div",I,[c("div",B,[u(_,{tag:"h1",size:"lg",class:"mb-6 text-center text-default"},{default:p(()=>e[1]||(e[1]=[d(" Login ")])),_:1,__:[1]}),c("form",{onSubmit:V(v,["prevent"]),class:"flex flex-col gap-4"},[u(g,{modelValue:o.value,"onUpdate:modelValue":e[0]||(e[0]=x=>o.value=x),type:"password",placeholder:"admin:pass",error:s.value,autofocus:"",class:"w-full"},null,8,["modelValue","error"]),u(w,{type:"submit",loading:t.value,class:"w-full"},{default:p(()=>e[2]||(e[2]=[d(" Login ")])),_:1,__:[2]},8,["loading"])],32)])])}}});export{U as default};
//# sourceMappingURL=Unauthenticated-D1fMGe3S.js.map
