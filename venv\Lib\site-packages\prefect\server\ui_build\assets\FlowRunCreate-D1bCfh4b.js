import{c9 as g,d as b,f as k,W as F,u as N,al as S,ca as x,ae as D,h as I,i as J,c as B,a as O,m as e,o as T,j as d,k as p,cb as j,cc as P,cd as Q,ce as U,be as i,K as f,L as V}from"./index-g6FNXcTE.js";import{u as W}from"./usePageTitle-DEr56mEx.js";class K extends g{parse(t){return JSON.parse(decodeURIComponent(t??""))}format(t){return encodeURIComponent(JSON.stringify(t))}}const X=b({__name:"FlowRunCreate",setup(y){const t=k(),l=F("deploymentId"),c=N(),w=S("parameters",K,void 0),{deployment:a}=x(l),o=D(!1),R=async u=>{var m,s;if(!o.value)try{o.value=!0;const n=await t.deployments.createDeploymentFlowRun(l.value,u),r=((s=(m=u.state)==null?void 0:m.stateDetails)==null?void 0:s.scheduledTime)??void 0,C=!r,h=Q(U,{flowRun:n,flowRunRoute:i.flowRun,router:c,immediate:C,startTime:r});f(h,"success"),c.push(i.deployment(l.value))}catch(n){const r=V(n,"Something went wrong trying to create a flow run");f(r,"error"),console.error(n)}finally{o.value=!1}},_=()=>{c.back()},v=I(()=>a.value?`Create Flow Run for Deployment: ${a.value.name}`:"Create Flow Run for Deployment");return W(v),(u,m)=>{const s=J("p-layout-default");return e(a)?(T(),B(s,{key:0},{header:d(()=>[p(e(P),{deployment:e(a)},null,8,["deployment"])]),default:d(()=>[p(e(j),{deployment:e(a),parameters:e(w),disabled:o.value,onSubmit:R,onCancel:_},null,8,["deployment","parameters","disabled"])]),_:1})):O("",!0)}}});export{X as default};
//# sourceMappingURL=FlowRunCreate-D1bCfh4b.js.map
