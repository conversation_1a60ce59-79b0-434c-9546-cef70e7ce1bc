import{d as k,f as c,W as l,J as w,i as _,c as d,o as i,j as r,k as n,m as o,d8 as f,d9 as P}from"./index-g6FNXcTE.js";import{u as Q}from"./usePageTitle-DEr56mEx.js";const v=k({__name:"WorkPoolQueueEdit",async setup(N){let e,u;const s=c(),a=l("workPoolName"),t=l("workPoolQueueName"),m=([e,u]=w(()=>s.workPoolQueues.getWorkPoolQueueByName(a.value,t.value)),e=await e,u(),e);return Q("Edit Work Pool Queue"),(y,W)=>{const p=_("p-layout-default");return i(),d(p,null,{header:r(()=>[n(o(P),{"work-pool-name":o(a),"work-pool-queue-name":o(t)},null,8,["work-pool-name","work-pool-queue-name"])]),default:r(()=>[n(o(f),{"work-pool-name":o(a),"work-pool-queue":o(m)},null,8,["work-pool-name","work-pool-queue"])]),_:1})}}});export{v as default};
//# sourceMappingURL=WorkPoolQueueEdit-_pkHSm3B.js.map
