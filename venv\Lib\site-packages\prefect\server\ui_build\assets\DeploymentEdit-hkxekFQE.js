import{d as v,f as h,W as b,g,h as r,i as C,c as D,a as k,o as w,j as p,k as c,m as u,c5 as E,c8 as x,K as m,c7 as d,be as V,L as W}from"./index-g6FNXcTE.js";import{u as j}from"./usePageTitle-DEr56mEx.js";const S=v({__name:"DeploymentEdit",setup(B){const a=h(),t=b("deploymentId"),i={interval:3e5},s=g(a.deployments.getDeployment,[t.value],i),e=r(()=>s.response);async function y(l){try{await a.deployments.updateDeploymentV2(t.value,l),m("Deployment updated","success"),s.refresh(),d.push(V.deployment(t.value))}catch(o){const n=W(o,"Error updating deployment");m(n,"error"),console.warn(o)}}function f(){d.back()}const _=r(()=>e.value?`Edit Deployment: ${e.value.name}`:"Edit Deployment");return j(_),(l,o)=>{const n=C("p-layout-default");return e.value?(w(),D(n,{key:0,class:"deployment-edit"},{header:p(()=>[c(u(x),{deployment:e.value},null,8,["deployment"])]),default:p(()=>[c(u(E),{deployment:e.value,onCancel:f,onSubmit:y},null,8,["deployment"])]),_:1})):k("",!0)}}});export{S as default};
//# sourceMappingURL=DeploymentEdit-hkxekFQE.js.map
