{"version": 3, "file": "usePrefectApi-iH-jzNq9.js", "sources": ["../../src/compositions/usePrefectApi.ts"], "sourcesContent": ["import { inject } from '@prefecthq/prefect-ui-library'\nimport { CreatePrefectApi, prefectApiKey } from '@/utilities/api'\n\nexport function usePrefectApi(): CreatePrefectApi {\n  return inject(prefectApiKey)\n}"], "names": ["usePrefectApi", "inject", "prefectApiKey"], "mappings": "+EAGO,SAASA,GAAkC,CAChD,OAAOC,EAAOC,CAAa,CAC7B"}