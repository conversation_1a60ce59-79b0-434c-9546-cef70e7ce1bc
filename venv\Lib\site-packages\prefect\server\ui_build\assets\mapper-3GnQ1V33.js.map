{"version": 3, "file": "mapper-3GnQ1V33.js", "sources": ["../../src/types/automation.ts", "../../src/maps/automation.ts", "../../src/maps/csrfToken.ts", "../../src/maps/featureFlag.ts", "../../src/maps/uiSettings.ts", "../../src/maps/index.ts", "../../src/services/mapper.ts"], "sourcesContent": ["import { AutomationAction, AutomationTrigger, Automation as BaseAutomation, IAutomation as BaseIAutomation } from '@prefecthq/prefect-ui-library'\n\nexport interface IAutomation extends BaseIAutomation {\n  trigger: AutomationTrigger,\n  actions: AutomationAction[],\n}\n\nexport class Automation extends BaseAutomation {\n  public trigger: AutomationTrigger\n  public actions: AutomationAction[]\n\n  public constructor(automation: IAutomation) {\n    super(automation)\n    this.trigger = automation.trigger\n    this.actions = automation.actions\n  }\n}\n\nexport type AutomationFormValues = {\n  id?: string,\n  name?: string,\n  description?: string,\n  trigger?: AutomationTrigger,\n  actions?: AutomationAction[],\n  enabled?: boolean,\n}\n\nexport type AutomationActionFormValues = AutomationFormValues & {\n  trigger: AutomationTrigger,\n}\n\nexport function isAutomationActionFormValues(value: AutomationFormValues): value is AutomationActionFormValues {\n  return Boolean(value.trigger)\n}", "import { MapFunction } from '@/services/mapper'\nimport { Automation } from '@/types/automation'\nimport { AutomationCreate } from '@/types/automationCreate'\nimport { AutomationCreateRequest } from '@/types/automationCreateRequest'\nimport { AutomationResponse } from '@/types/automationResponse'\n\nexport const mapAutomationResponseToAutomation: MapFunction<AutomationResponse, Automation> = function(source) {\n  return new Automation({\n    id: source.id,\n    name: source.name,\n    description: source.description,\n    enabled: source.enabled,\n    trigger: this.map('AutomationTriggerResponse', source.trigger, 'AutomationTrigger'),\n    actions: this.map('AutomationActionResponse', source.actions, 'AutomationAction'),\n  })\n}\n\nexport const mapAutomationCreateToAutomationCreateRequest: MapFunction<AutomationCreate, AutomationCreateRequest> = function(source) {\n  return {\n    name: source.name,\n    description: source.description,\n    enabled: source.enabled,\n    trigger: this.map('AutomationTrigger', source.trigger, 'AutomationTriggerRequest'),\n    actions: this.map('AutomationAction', source.actions, 'AutomationActionRequest'),\n  }\n}", "import { CsrfToken } from '@/models/CsrfToken'\nimport { MapFunction } from '@/services/mapper'\nimport { CsrfTokenResponse } from '@/types/csrfTokenResponse'\n\nexport const mapCsrfTokenResponseToCsrfToken: MapFunction<CsrfTokenResponse, CsrfToken> = function(source) {\n  return {\n    token: source.token,\n    expiration: this.map('string', source.expiration, 'Date'),\n    issued: new Date(),\n  }\n}\n\n", "import { WorkspaceFeatureFlag } from '@prefecthq/prefect-ui-library'\nimport { MapFunction } from '@/services/mapper'\nimport { FlagResponse } from '@/types/flagResponse'\nimport { FeatureFlag } from '@/utilities/permissions'\n\nexport const mapFlagResponseToFeatureFlag: MapFunction<FlagResponse, FeatureFlag | WorkspaceFeatureFlag | null> = (source) => {\n  switch (source) {\n    case 'workers':\n      return 'access:workers'\n    case 'artifacts':\n      return 'access:artifacts'\n    default:\n      // we want to have a ts error here but we don't want to break the app so returning null rather than throwing an error\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars\n      const exhaustiveCheck: never = source\n      return null\n  }\n}\n", "import { isNotNullish } from '@prefecthq/prefect-ui-library'\nimport { MapFunction } from '@/services/mapper'\nimport { Settings } from '@/services/uiSettings'\nimport { SettingsResponse } from '@/types/settingsResponse'\n\nexport const mapSettingsResponseToSettings: MapFunction<SettingsResponse, Settings> = function(source) {\n  return {\n    apiUrl: source.api_url,\n    csrfEnabled: source.csrf_enabled,\n    auth: source.auth,\n    flags: this.map('FlagResponse', source.flags, 'FeatureFlag').filter(isNotNullish),\n  }\n}", "import { maps as designMaps } from '@prefecthq/prefect-ui-library'\nimport { mapAutomationCreateToAutomationCreateRequest, mapAutomationResponseToAutomation } from '@/maps/automation'\nimport { mapCsrfTokenResponseToCsrfToken } from '@/maps/csrfToken'\nimport { mapFlagResponseToFeatureFlag } from '@/maps/featureFlag'\nimport { mapSettingsResponseToSettings } from '@/maps/uiSettings'\n\nexport const maps = {\n  ...designMaps,\n  FlagResponse: { FeatureFlag: mapFlagResponseToFeatureFlag },\n  SettingsResponse: { Settings: mapSettingsResponseToSettings },\n  CsrfTokenResponse: { CsrfToken: mapCsrfTokenResponseToCsrfToken },\n  AutomationResponse: { Automation: mapAutomationResponseToAutomation },\n  AutomationCreate: { AutomationCreateRequest: mapAutomationCreateToAutomationCreateRequest },\n}\n", "import { Mapper } from '@prefecthq/prefect-ui-library'\nimport { maps } from '@/maps'\n\nexport const mapper = new Mapper(maps)\n\nexport type MapFunction<S, D> = (this: typeof mapper, source: S) => D"], "names": ["Automation", "BaseAutomation", "automation", "__publicField", "isAutomationActionFormValues", "value", "mapAutomationResponseToAutomation", "source", "mapAutomationCreateToAutomationCreateRequest", "mapCsrfTokenResponseToCsrfToken", "mapFlagResponseToFeatureFlag", "mapSettingsResponseToSettings", "isNot<PERSON><PERSON>ish", "maps", "designMaps", "mapper", "<PERSON><PERSON>"], "mappings": "qOAOO,MAAMA,UAAmBC,CAAe,CAItC,YAAYC,EAAyB,CAC1C,MAAMA,CAAU,EAJXC,EAAA,gBACAA,EAAA,gBAIL,KAAK,QAAUD,EAAW,QAC1B,KAAK,QAAUA,EAAW,OAAA,CAE9B,CAeO,SAASE,EAA6BC,EAAkE,CACtG,MAAA,EAAQA,EAAM,OACvB,CC3Ba,MAAAC,EAAiF,SAASC,EAAQ,CAC7G,OAAO,IAAIP,EAAW,CACpB,GAAIO,EAAO,GACX,KAAMA,EAAO,KACb,YAAaA,EAAO,YACpB,QAASA,EAAO,QAChB,QAAS,KAAK,IAAI,4BAA6BA,EAAO,QAAS,mBAAmB,EAClF,QAAS,KAAK,IAAI,2BAA4BA,EAAO,QAAS,kBAAkB,CAAA,CACjF,CACH,EAEaC,EAAuG,SAASD,EAAQ,CAC5H,MAAA,CACL,KAAMA,EAAO,KACb,YAAaA,EAAO,YACpB,QAASA,EAAO,QAChB,QAAS,KAAK,IAAI,oBAAqBA,EAAO,QAAS,0BAA0B,EACjF,QAAS,KAAK,IAAI,mBAAoBA,EAAO,QAAS,yBAAyB,CACjF,CACF,ECrBaE,EAA6E,SAASF,EAAQ,CAClG,MAAA,CACL,MAAOA,EAAO,MACd,WAAY,KAAK,IAAI,SAAUA,EAAO,WAAY,MAAM,EACxD,WAAY,IACd,CACF,ECLaG,EAAsGH,GAAW,CAC5H,OAAQA,EAAQ,CACd,IAAK,UACI,MAAA,iBACT,IAAK,YACI,MAAA,mBACT,QAIS,OAAA,IAAA,CAEb,ECZaI,EAAyE,SAASJ,EAAQ,CAC9F,MAAA,CACL,OAAQA,EAAO,QACf,YAAaA,EAAO,aACpB,KAAMA,EAAO,KACb,MAAO,KAAK,IAAI,eAAgBA,EAAO,MAAO,aAAa,EAAE,OAAOK,CAAY,CAClF,CACF,ECNaC,EAAO,CAClB,GAAGC,EACH,aAAc,CAAE,YAAaJ,CAA6B,EAC1D,iBAAkB,CAAE,SAAUC,CAA8B,EAC5D,kBAAmB,CAAE,UAAWF,CAAgC,EAChE,mBAAoB,CAAE,WAAYH,CAAkC,EACpE,iBAAkB,CAAE,wBAAyBE,CAA6C,CAC5F,ECVaO,EAAS,IAAIC,EAAOH,CAAI"}