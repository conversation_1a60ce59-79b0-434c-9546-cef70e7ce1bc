import{d as L,u as A,W as B,f as V,h as s,aI as X,al as j,bt as E,bF as b,bG as H,i,c,a as p,o as k,j as a,k as n,m as e,a6 as J,E as P,by as W,bH as G,bI as K,bJ as _,B as R,bK as O,t as U,H as Z,bL as $,be as q}from"./index-g6FNXcTE.js";import{u as z}from"./usePageTitle-DEr56mEx.js";const tt=L({__name:"TaskRun",setup(M){const m=A(),d=B("taskRunId"),v=V(),g=s(()=>[{label:"Details",hidden:X.xl},{label:"Logs"},{label:"Artifacts"},{label:"Task Inputs"}]),l=j("tab","Logs"),{tabs:w}=E(g,l),I=s(()=>d.value?[d.value]:null),h=b(v.taskRuns.getTaskRun,I,{interval:3e4}),t=s(()=>h.response),r=s(()=>{var u;return(u=t.value)==null?void 0:u.flowRunId}),y=s(()=>r.value?[r.value]:null),T=b(v.flowRuns.getFlowRun,y),f=s(()=>{var u;return(u=t.value)!=null&&u.taskInputs?JSON.stringify(t.value.taskInputs,void 0,2):"{}"});function x(){T.refresh(),m.push(q.flowRun(r.value))}H(t);const D=s(()=>t.value?`Task Run: ${t.value.name}`:"Task Run");return z(D),(u,o)=>{const C=i("p-code-highlight"),F=i("p-tabs"),N=i("p-layout-well");return t.value?(k(),c(N,{key:0,class:"task-run"},{header:a(()=>[n(e($),{"task-run-id":t.value.id,onDelete:x},null,8,["task-run-id"])]),well:a(()=>[n(e(_),{alternate:"","task-run":t.value},null,8,["task-run"])]),default:a(()=>[n(F,{selected:e(l),"onUpdate:selected":o[0]||(o[0]=S=>J(l)?l.value=S:null),tabs:e(w)},P({details:a(()=>[n(e(_),{"task-run":t.value},null,8,["task-run"])]),logs:a(()=>[n(e(K),{"task-run":t.value},null,8,["task-run"])]),artifacts:a(()=>[t.value?(k(),c(e(G),{key:0,"task-run":t.value},null,8,["task-run"])):p("",!0)]),"task-inputs":a(()=>[t.value?(k(),c(e(W),{key:0,"text-to-copy":f.value},{default:a(()=>[n(C,{lang:"json",text:f.value,class:"task-run__inputs"},null,8,["text"])]),_:1},8,["text-to-copy"])):p("",!0)]),_:2},[t.value?{name:"task-inputs-heading",fn:a(()=>[o[1]||(o[1]=R(" Task inputs ")),n(e(O),{title:"Task Inputs"},{default:a(()=>[R(U(e(Z).info.taskInput),1)]),_:1})]),key:"0"}:void 0]),1032,["selected","tabs"])]),_:1})):p("",!0)}}});export{tt as default};
//# sourceMappingURL=TaskRun-CfR6GSDh.js.map
