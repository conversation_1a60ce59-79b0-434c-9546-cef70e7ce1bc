{"version": 3, "file": "Deployments-DZ_KO9eC.js", "sources": ["../../src/pages/Deployments.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"deployments\">\n    <template #header>\n      <PageHeadingDeployments />\n    </template>\n\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <DeploymentsPageEmptyState />\n      </template>\n\n      <template v-else>\n        <DeploymentList @delete=\"deploymentsSubscription.refresh\" />\n      </template>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { DeploymentList, PageHeadingDeployments, DeploymentsPageEmptyState, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const subscriptionOptions = {\n    interval: 30000,\n  }\n\n  const deploymentsSubscription = useSubscription(api.deployments.getDeployments, [{}], subscriptionOptions)\n  const deployments = computed(() => deploymentsSubscription.response ?? [])\n  const empty = computed(() => deploymentsSubscription.executed && deployments.value.length === 0)\n  const loaded = computed(() => deploymentsSubscription.executed)\n\n  usePageTitle('Deployments')\n</script>"], "names": ["api", "useWorkspaceApi", "subscriptionOptions", "deploymentsSubscription", "useSubscription", "deployments", "computed", "empty", "loaded", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingDeployments", "_createElementBlock", "_Fragment", "DeploymentsPageEmptyState", "DeploymentList"], "mappings": "uOAwBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAA0BC,EAAgBJ,EAAI,YAAY,eAAgB,CAAC,CAAA,CAAE,EAAGE,CAAmB,EACnGG,EAAcC,EAAS,IAAMH,EAAwB,UAAY,CAAA,CAAE,EACnEI,EAAQD,EAAS,IAAMH,EAAwB,UAAYE,EAAY,MAAM,SAAW,CAAC,EACzFG,EAASF,EAAS,IAAMH,EAAwB,QAAQ,EAE9D,OAAAM,EAAa,aAAa,iDAjC1B,EAAAC,EAcmBC,EAAA,CAdD,MAAM,eAAa,CACxB,SACT,IAA0B,CAA1BC,EAA0BC,EAAAC,CAAA,CAAA,CAAA,aAG5B,IAQW,CARKN,EAAM,WAAtBO,EAQWC,EAAA,CAAA,IAAA,GAAA,CAPOT,EAAK,WACnBG,EAA6BG,EAAAI,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA,QAI7BP,EAA4DG,EAAAK,CAAA,EAAA,OAA3C,SAAQL,EAAuBV,CAAA,EAAC"}