import{d as V,V as B,u as D,W as N,g as T,h as n,i as s,c as _,a as q,o as l,j as e,k as a,l as d,m as o,n as F,q as I,t as M,v as P,F as U,x as W,y as j,z as w,M as z,A as E}from"./index-g6FNXcTE.js";import{u as L}from"./usePageTitle-DEr56mEx.js";import{u as R}from"./usePrefectApi-iH-jzNq9.js";import"./api-CQZ_ymP4.js";import"./mapper-3GnQ1V33.js";const S={class:"automation-card__label"},Q=V({__name:"Automation",setup($){const c=B(),f=D(),v=R(),g=N("automationId"),i=T(v.automations.getAutomation,[g]),t=n(()=>i.response),y=n(()=>{var u;return((u=t.value)==null?void 0:u.name)??""}),A=n(()=>[{text:"Automations",to:c.automations()},{text:y.value}]),b=n(()=>t.value?`Automation: ${t.value.name}`:"Automation");L(b);function k(){f.push(c.automations())}return(u,m)=>{const h=s("p-key-value"),r=s("p-content"),x=s("p-card"),C=s("p-layout-default");return t.value?(l(),_(C,{key:0,class:"automation"},{header:e(()=>[a(o(w),{crumbs:A.value},{actions:e(()=>[a(o(z),{automation:t.value,onUpdate:o(i).refresh},null,8,["automation","onUpdate"]),a(o(E),{automation:t.value,onDelete:k},null,8,["automation"])]),_:1},8,["crumbs"])]),default:e(()=>[a(r,null,{default:e(()=>[a(h,{label:"Description",value:t.value.description},null,8,["value"]),a(r,{secondary:""},{default:e(()=>[m[0]||(m[0]=d("span",{class:"automation-card__label"},"Trigger",-1)),a(o(F),{trigger:t.value.trigger},null,8,["trigger"])]),_:1,__:[0]}),a(r,{secondary:""},{default:e(()=>[d("span",S,M(o(P)("Action",t.value.actions.length)),1),(l(!0),I(U,null,W(t.value.actions,p=>(l(),_(x,{key:p.id},{default:e(()=>[a(o(j),{action:p},null,8,["action"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})):q("",!0)}}});export{Q as default};
//# sourceMappingURL=Automation-BGYSrmvK.js.map
