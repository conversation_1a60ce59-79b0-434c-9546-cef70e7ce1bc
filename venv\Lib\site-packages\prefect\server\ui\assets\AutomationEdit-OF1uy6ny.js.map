{"version": 3, "file": "AutomationEdit-OF1uy6ny.js", "sources": ["../../src/pages/AutomationEdit.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"workspace-automation-create\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\">\n        <template #actions>\n          <DocumentationButton :to=\"localization.docs.automations\">\n            Documentation\n          </DocumentationButton>\n        </template>\n      </PageHeading>\n    </template>\n\n    <AutomationWizard :automation=\"automation\" editing @submit=\"submit\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { BreadCrumbs, showToast } from '@prefecthq/prefect-design'\n  import { PageHeading, DocumentationButton, getApiErrorMessage, useWorkspaceRoutes, localization } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import AutomationWizard from '@/components/AutomationWizard.vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { usePrefectApi } from '@/compositions/usePrefectApi'\n  import { Automation } from '@/types/automation'\n\n  const api = usePrefectApi()\n  const routes = useWorkspaceRoutes()\n  const router = useRouter()\n  const automationId = useRouteParam('automationId')\n  const automation = await api.automations.getAutomation(automationId.value)\n\n  usePageTitle(`Edit Automation: ${automation.name}`)\n\n  const crumbs = computed<BreadCrumbs>(() => [\n    { text: 'Automations', to: routes.automations() },\n    { text: automation.name },\n  ])\n\n  async function submit(automation: Automation): Promise<void> {\n    try {\n      await api.automations.updateAutomation(automationId.value, automation)\n\n      showToast(localization.success.automationUpdate)\n\n      router.push(routes.automations())\n    } catch (error) {\n      console.error(error)\n      const message = getApiErrorMessage(error, localization.error.automationUpdate)\n      showToast(message, 'error', { timeout: false })\n    }\n  }\n</script>"], "names": ["api", "usePrefectApi", "routes", "useWorkspaceRoutes", "router", "useRouter", "automationId", "useRouteParam", "automation", "__temp", "__restore", "_withAsyncContext", "usePageTitle", "crumbs", "computed", "submit", "showToast", "localization", "error", "message", "getApiErrorMessage", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeading", "DocumentationButton", "_cache", "AutomationWizard"], "mappings": "scA2BE,MAAMA,EAAMC,EAAc,EACpBC,EAASC,EAAmB,EAC5BC,EAASC,EAAU,EACnBC,EAAeC,EAAc,cAAc,EAC3CC,GAAmB,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAAX,EAAI,YAAY,cAAcM,EAAa,KAAK,CAAA,mBAE5DM,EAAA,oBAAoBJ,EAAW,IAAI,EAAE,EAE5C,MAAAK,EAASC,EAAsB,IAAM,CACzC,CAAE,KAAM,cAAe,GAAIZ,EAAO,aAAc,EAChD,CAAE,KAAMM,EAAW,IAAK,CAAA,CACzB,EAED,eAAeO,EAAOP,EAAuC,CACvD,GAAA,CACF,MAAMR,EAAI,YAAY,iBAAiBM,EAAa,MAAOE,CAAU,EAE3DQ,EAAAC,EAAa,QAAQ,gBAAgB,EAExCb,EAAA,KAAKF,EAAO,aAAa,QACzBgB,EAAO,CACd,QAAQ,MAAMA,CAAK,EACnB,MAAMC,EAAUC,EAAmBF,EAAOD,EAAa,MAAM,gBAAgB,EAC7ED,EAAUG,EAAS,QAAS,CAAE,QAAS,GAAO,CAAA,CAChD,sDAlDF,EAAAE,EAYmBC,EAAA,CAZD,MAAM,+BAA6B,CACxC,SACT,IAMc,CANdC,EAMcC,EAAAC,CAAA,EAAA,CANA,OAAQZ,EAAM,OAAA,CACf,UACT,IAEsB,CAFtBU,EAEsBC,EAAAE,CAAA,EAAA,CAFA,GAAIF,EAAAP,CAAA,EAAa,KAAK,WAAA,aAAa,IAEzDU,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAFyD,iBAEzD,CAAA,2DAKN,IAAsE,CAAtEJ,EAAsEK,EAAA,CAAnD,WAAYJ,EAAUhB,CAAA,EAAE,QAAA,GAAS,SAAQO,CAAA"}