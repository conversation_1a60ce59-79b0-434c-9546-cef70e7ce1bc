import{d as v,aR as y,aS as V,f as C,g as F,h as i,aT as x,i as d,c as _,o as n,j as l,q as m,a as R,k as a,F as S,m as e,aU as B,l as r,aV as U,aW as A,e as E,aX as N,aY as T,B as D,z as W,E as j,aZ as q,ay as z}from"./index-g6FNXcTE.js";import{s as P}from"./index-B4HswuBc.js";const G={class:"workspace-dashboard__header-actions"},H={class:"workspace-dashboard__subflows-toggle"},I={key:1,class:"workspace-dashboard__grid"},J={class:"workspace-dashboard__side"},Z=v({__name:"Dashboard",setup(K){y(V,{interval:P(30)});const f=C(),c=F(f.flowRuns.getFlowRunsCount,[{}]),u=i(()=>c.executed),p=i(()=>c.response===0),g=[{text:"Dashboard"}],s=x({range:{type:"span",seconds:-86400},tags:[]}),b=()=>E.map("WorkspaceDashboardFilter",s,"TaskRunsFilter");return(M,o)=>{const k=d("p-toggle"),w=d("p-button"),h=d("p-layout-default");return n(),_(h,{class:"workspace-dashboard"},{header:l(()=>[a(e(W),{crumbs:g,class:"workspace-dashboard__page-heading"},j({_:2},[u.value&&!p.value?{name:"actions",fn:l(()=>[r("div",G,[r("div",H,[a(k,{modelValue:e(s).hideSubflows,"onUpdate:modelValue":o[0]||(o[0]=t=>e(s).hideSubflows=t),append:"Hide subflows"},null,8,["modelValue"])]),a(e(q),{selected:e(s).tags,"onUpdate:selected":o[1]||(o[1]=t=>e(s).tags=t),"empty-message":"All tags"},null,8,["selected"]),a(e(z),{modelValue:e(s).range,"onUpdate:modelValue":o[2]||(o[2]=t=>e(s).range=t),class:"workspace-dashboard__date-select"},null,8,["modelValue"])])]),key:"0"}:void 0]),1024)]),default:l(()=>[u.value?(n(),m(S,{key:0},[p.value?(n(),_(e(B),{key:0})):(n(),m("div",I,[a(e(U),{filter:e(s)},null,8,["filter"]),r("div",J,[a(e(A),{filter:b}),a(e(N),{class:"workspace-dashboard__work-pools",filter:e(s)},null,8,["filter"])])]))],64)):R("",!0),a(e(T),{title:"Ready to scale?",subtitle:"Webhooks, role and object-level security, and serverless push work pools on Prefect Cloud"},{actions:l(()=>[a(w,{to:"https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss&utm_term=none&utm_content=none",target:"_blank",primary:""},{default:l(()=>o[3]||(o[3]=[D(" Upgrade to Cloud ")])),_:1,__:[3]})]),_:1})]),_:1})}}});export{Z as default};
//# sourceMappingURL=Dashboard-BxGI5M_J.js.map
