{"version": 3, "file": "ConcurrencyLimits-BpiiZtoz.js", "sources": ["../../src/pages/ConcurrencyLimits.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"concurrency-limits\">\n    <template #header>\n      <PageHeading :crumbs=\"[{ text: 'Concurrency' }]\" />\n    </template>\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #global>\n        <PageHeading size=\"lg\" :crumbs=\"[{ text: 'Global Concurrency Limits' }]\">\n          <template #after-crumbs>\n            <p-button small icon=\"PlusIcon\" @click=\"openGlobal\" />\n          </template>\n        </PageHeading>\n        <ConcurrencyLimitsV2CreateModal v-model:showModal=\"showModalGlobal\" />\n        <ConcurrencyLimitsV2Table class=\"concurrency-limits__global-table\" />\n      </template>\n      <template #task-run>\n        <PageHeading size=\"lg\" :crumbs=\"[{ text: 'Task Run Concurrency Limits' }]\">\n          <template #after-crumbs>\n            <p-button small icon=\"PlusIcon\" @click=\"openTaskRun\" />\n          </template>\n        </PageHeading>\n        <ConcurrencyLimitsCreateModal v-model:showModal=\"showModalTaskRun\" />\n        <ConcurrencyLimitsTable class=\"concurrency-limits__task-limits-table\" />\n      </template>\n    </p-tabs>\n  </p-layout-default>\n</template>\n\n  <script lang=\"ts\" setup>\n  import { PageHeading, ConcurrencyLimitsV2Table, ConcurrencyLimitsTable, ConcurrencyLimitsCreateModal, ConcurrencyLimitsV2CreateModal, useShowModal } from '@prefecthq/prefect-ui-library'\n  import { useRouteQueryParam } from '@prefecthq/vue-compositions'\n\n  const { showModal: showModalGlobal, open: openGlobal } = useShowModal()\n  const { showModal: showModalTaskRun, open: openTaskRun } = useShowModal()\n\n  const tabs = [\n    { label: 'Global' },\n    { label: 'Task Run' },\n  ]\n\n  const tab = useRouteQueryParam('tab', 'Global')\n</script>\n\n<style>\n.concurrency-limits__global-table { @apply\n  mb-2\n  mt-4\n}\n\n.concurrency-limits__task-limits-table { @apply\n  mb-2\n  mt-4\n}\n</style>"], "names": ["showModalGlobal", "openGlobal", "useShowModal", "showModalTaskRun", "openTaskRun", "tabs", "tab", "useRouteQueryParam", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeading", "_component_p_tabs", "$event", "_component_p_button", "ConcurrencyLimitsV2CreateModal", "ConcurrencyLimitsV2Table", "ConcurrencyLimitsCreateModal", "ConcurrencyLimitsTable"], "mappings": "gMAgCE,KAAM,CAAE,UAAWA,EAAiB,KAAMC,CAAA,EAAeC,EAAa,EAChE,CAAE,UAAWC,EAAkB,KAAMC,CAAA,EAAgBF,EAAa,EAElEG,EAAO,CACX,CAAE,MAAO,QAAS,EAClB,CAAE,MAAO,UAAW,CACtB,EAEMC,EAAMC,EAAmB,MAAO,QAAQ,qFAvC9C,EAAAC,EAwBmBC,EAAA,CAxBD,MAAM,sBAAoB,CAC/B,SACT,IAAmD,CAAnDC,EAAmDC,EAAAC,CAAA,EAAA,CAArC,OAAQ,CAAyB,CAAA,KAAA,aAAA,CAAA,CAAA,CAAA,CAAA,aAEjD,IAmBS,CAnBTF,EAmBSG,EAAA,CAnBO,SAAUF,EAAGL,CAAA,0CAAHA,EAAG,MAAAQ,EAAA,MAAG,KAAAT,CAAA,GACnB,SACT,IAIc,CAJdK,EAIcC,EAAAC,CAAA,EAAA,CAJD,KAAK,KAAM,OAAQ,CAAuC,CAAA,KAAA,2BAAA,CAAA,CAAA,GAC1D,iBACT,IAAsD,CAAtDF,EAAsDK,EAAA,CAA5C,MAAA,GAAM,KAAK,WAAY,QAAOJ,EAAUV,CAAA,CAAA,8BAGtDS,EAAsEC,EAAAK,CAAA,EAAA,CAA9B,UAAWL,EAAeX,CAAA,2CAAfA,EAAe,MAAAc,EAAA,6BAClEJ,EAAqEC,EAAAM,CAAA,EAAA,CAA3C,MAAM,kCAAkC,CAAA,CAAA,GAEzD,aACT,IAIc,CAJdP,EAIcC,EAAAC,CAAA,EAAA,CAJD,KAAK,KAAM,OAAQ,CAAyC,CAAA,KAAA,6BAAA,CAAA,CAAA,GAC5D,iBACT,IAAuD,CAAvDF,EAAuDK,EAAA,CAA7C,MAAA,GAAM,KAAK,WAAY,QAAOJ,EAAWP,CAAA,CAAA,8BAGvDM,EAAqEC,EAAAO,CAAA,EAAA,CAA/B,UAAWP,EAAgBR,CAAA,2CAAhBA,EAAgB,MAAAW,EAAA,6BACjEJ,EAAwEC,EAAAQ,CAAA,EAAA,CAAhD,MAAM,uCAAuC,CAAA,CAAA"}