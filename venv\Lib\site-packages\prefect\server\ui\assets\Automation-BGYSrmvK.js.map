{"version": 3, "file": "Automation-BGYSrmvK.js", "sources": ["../../src/pages/Automation.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"automation\" class=\"automation\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\">\n        <template #actions>\n          <AutomationToggle :automation=\"automation\" @update=\"subscription.refresh\" />\n          <AutomationMenu :automation=\"automation\" @delete=\"goToAutomations\" />\n        </template>\n      </PageHeading>\n    </template>\n    <p-content>\n      <p-key-value label=\"Description\" :value=\"automation.description\" />\n\n      <p-content secondary>\n        <span class=\"automation-card__label\">Trigger</span>\n        <AutomationTriggerDescription :trigger=\"automation.trigger\" />\n      </p-content>\n\n      <p-content secondary>\n        <span class=\"automation-card__label\">{{ toPluralString('Action', automation.actions.length) }}</span>\n        <template v-for=\"action in automation.actions\" :key=\"action.id\">\n          <p-card><AutomationActionDescription :action=\"action\" /></p-card>\n        </template>\n      </p-content>\n    </p-content>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { Crumb } from '@prefecthq/prefect-design'\n  import { PageHeading, AutomationMenu, AutomationToggle, AutomationTriggerDescription, AutomationActionDescription, toPluralString, useWorkspaceRoutes } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { usePrefectApi } from '@/compositions/usePrefectApi'\n\n  const routes = useWorkspaceRoutes()\n  const router = useRouter()\n  const api = usePrefectApi()\n  const automationId = useRouteParam('automationId')\n\n  const subscription = useSubscription(api.automations.getAutomation, [automationId])\n  const automation = computed(() => subscription.response)\n\n  const name = computed(() => automation.value?.name ?? '')\n\n  const crumbs = computed<Crumb[]>(() => [\n    { text: 'Automations', to: routes.automations() },\n    { text: name.value },\n  ])\n\n  const title = computed<string>(() => {\n    if (automation.value) {\n      return `Automation: ${automation.value.name}`\n    }\n\n    return 'Automation'\n  })\n\n  usePageTitle(title)\n\n  function goToAutomations(): void {\n    router.push(routes.automations())\n  }\n</script>"], "names": ["routes", "useWorkspaceRoutes", "router", "useRouter", "api", "usePrefectApi", "automationId", "useRouteParam", "subscription", "useSubscription", "automation", "computed", "name", "_a", "crumbs", "title", "usePageTitle", "goToAutomations", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeading", "AutomationToggle", "AutomationMenu", "_component_p_content", "_component_p_key_value", "_cache", "_createElementVNode", "AutomationTriggerDescription", "_hoisted_1", "_toDisplayString", "toPluralString", "_openBlock", "_createElementBlock", "_Fragment", "_renderList", "action", "_component_p_card", "AutomationActionDescription"], "mappings": "0aAqCE,MAAMA,EAASC,EAAmB,EAC5BC,EAASC,EAAU,EACnBC,EAAMC,EAAc,EACpBC,EAAeC,EAAc,cAAc,EAE3CC,EAAeC,EAAgBL,EAAI,YAAY,cAAe,CAACE,CAAY,CAAC,EAC5EI,EAAaC,EAAS,IAAMH,EAAa,QAAQ,EAEjDI,EAAOD,EAAS,IAAA,OAAM,QAAAE,EAAAH,EAAW,QAAX,YAAAG,EAAkB,OAAQ,GAAE,EAElDC,EAASH,EAAkB,IAAM,CACrC,CAAE,KAAM,cAAe,GAAIX,EAAO,aAAc,EAChD,CAAE,KAAMY,EAAK,KAAM,CAAA,CACpB,EAEKG,EAAQJ,EAAiB,IACzBD,EAAW,MACN,eAAeA,EAAW,MAAM,IAAI,GAGtC,YACR,EAEDM,EAAaD,CAAK,EAElB,SAASE,GAAwB,CACxBf,EAAA,KAAKF,EAAO,aAAa,CAAA,sGA9DVU,EAAU,WAAlCQ,EAwBmBC,EAAA,OAxBiB,MAAM,YAAA,GAC7B,SACT,IAKc,CALdC,EAKcC,EAAAC,CAAA,EAAA,CALA,OAAQR,EAAM,OAAA,CACf,UACT,IAA4E,CAA5EM,EAA4EC,EAAAE,CAAA,EAAA,CAAzD,WAAYb,EAAU,MAAG,SAAQW,EAAYb,CAAA,EAAC,2CACjEY,EAAqEC,EAAAG,CAAA,EAAA,CAApD,WAAYd,EAAU,MAAG,SAAQO,CAAA,0DAIxD,IAcY,CAdZG,EAcYK,EAAA,KAAA,WAbV,IAAmE,CAAnEL,EAAmEM,EAAA,CAAtD,MAAM,cAAe,MAAOhB,EAAU,MAAC,+BAEpDU,EAGYK,EAAA,CAHD,UAAA,IAAS,WAClB,IAAmD,CAAnDE,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAAC,EAAmD,OAA7C,CAAA,MAAM,0BAAyB,UAAO,EAAA,GAC5CR,EAA8DC,EAAAQ,CAAA,EAAA,CAA/B,QAASnB,EAAU,MAAC,OAAA,qCAGrDU,EAKYK,EAAA,CALD,UAAA,IAAS,WAClB,IAAqG,CAArGG,EAAqG,OAArGE,EAAwCC,EAAAV,EAAAW,CAAA,WAAyBtB,EAAU,MAAC,QAAQ,MAAM,CAAA,EAAA,CAAA,GAC1FuB,EAAA,EAAA,EAAAC,EAEWC,EAFgB,KAAAC,EAAA1B,EAAA,MAAW,QAArB2B,QACfnB,EAAiEoB,EAAA,CADd,IAAAD,EAAO,EAAA,aAClD,IAAgD,CAAhDjB,EAAgDC,EAAAkB,CAAA,EAAA,CAAlB,OAAAF,CAAc,EAAA,KAAA,EAAA,CAAA,QAAA,CAAA,CAAA"}