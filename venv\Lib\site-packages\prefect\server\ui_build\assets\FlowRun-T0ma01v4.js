import{d as j,f as z,ae as k,h as c,q as D,o as f,l as R,c as p,a as d,k as a,m as l,bk as P,bl as A,bm as $,bn as q,bo as G,bp as L,bq as S,aw as W,u as Z,W as J,br as H,ah as x,bs as K,al as M,bt as O,bu as Q,bv as X,bw as Y,bx as ee,be as U,i as y,j as o,a6 as le,by as F,bz as te,bA as se,bB as ne,bC as ae,bD as oe,bE as ue}from"./index-g6FNXcTE.js";import{u as re}from"./usePageTitle-DEr56mEx.js";const ie={class:"flow-run-graphs__graph-panel-container"},fe={class:"flow-run-graphs__graphs"},ce={class:"flow-run-graphs__panel p-background"},pe=j({__name:"FlowRunGraphs",props:{flowRun:{}},setup(C){const m=z(),g=k(),s=k(!1),e=k(null),_=c(()=>{var r,t;return{root:{"flow-run-graphs--fullscreen":s.value,"flow-run-graphs--show-panel":((r=e.value)==null?void 0:r.kind)==="task-run"||((t=e.value)==null?void 0:t.kind)==="flow-run"}}}),v=async({nodeId:r,since:t,until:i})=>{const w={anyResource:{id:[`prefect.flow-run.${r}`]},event:{excludePrefix:["prefect.log.write","prefect.task-run."]},occurred:{since:t,until:i}},{events:b}=await m.events.getEvents(w);return b};return(r,t)=>{var i,w,b;return f(),D("div",{class:W(["flow-run-graphs",_.value.root])},[R("div",ie,[R("div",fe,[a(l(P),{fullscreen:s.value,"onUpdate:fullscreen":t[0]||(t[0]=n=>s.value=n),viewport:g.value,"onUpdate:viewport":t[1]||(t[1]=n=>g.value=n),selected:e.value,"onUpdate:selected":t[2]||(t[2]=n=>e.value=n),"flow-run":r.flowRun,"fetch-events":v,class:"flow-run-graphs__flow-run"},null,8,["fullscreen","viewport","selected","flow-run"])]),R("div",ce,[((i=e.value)==null?void 0:i.kind)==="task-run"||((w=e.value)==null?void 0:w.kind)==="flow-run"?(f(),p(l(A),{key:0,selection:e.value,"onUpdate:selection":t[3]||(t[3]=n=>e.value=n),floating:s.value},null,8,["selection","floating"])):d("",!0)])]),e.value&&e.value.kind==="event"?(f(),p(l($),{key:0,selection:e.value,"onUpdate:selection":t[4]||(t[4]=n=>e.value=n)},null,8,["selection"])):d("",!0),e.value&&e.value.kind==="events"?(f(),p(l(q),{key:1,selection:e.value,"onUpdate:selection":t[5]||(t[5]=n=>e.value=n)},null,8,["selection"])):d("",!0),e.value&&e.value.kind==="artifacts"?(f(),p(l(G),{key:2,selection:e.value,"onUpdate:selection":t[6]||(t[6]=n=>e.value=n)},null,8,["selection"])):d("",!0),((b=e.value)==null?void 0:b.kind)==="state"?(f(),p(l(L),{key:3,selection:e.value,"onUpdate:selection":t[7]||(t[7]=n=>e.value=n)},null,8,["selection"])):d("",!0),a(l(S),{selection:e.value,"onUpdate:selection":t[8]||(t[8]=n=>e.value=n)},null,8,["selection"])],2)}}}),we=j({__name:"FlowRun",setup(C){const m=Z(),g=J("flowRunId"),{flowRun:s,subscription:e}=H(g,{interval:5e3}),_=c(()=>{var u;return x(((u=s.value)==null?void 0:u.parameters)??{})}),v=c(()=>{var u;return(u=s.value)!=null&&u.stateType?K(s.value.stateType):!0}),r=c(()=>{var u;return x(((u=s.value)==null?void 0:u.jobVariables)??{})}),t=c(()=>[{label:"Logs"},{label:"Task Runs",hidden:v.value},{label:"Subflow Runs",hidden:v.value},{label:"Artifacts",hidden:v.value},{label:"Details"},{label:"Parameters"},{label:"Job Variables"}]),i=M("tab","Logs"),{tabs:w}=O(t,i),b=c(()=>[g.value]),{filter:n}=Q({flowRuns:{parentFlowRunId:b}});function T(){m.push(U.runs())}X(s);const V=c(()=>s.value?`Flow Run: ${s.value.name}`:"Flow Run");return re(V),Y(()=>{e.error&&ee(e.error).isInRange("clientError")&&m.replace(U[404]())}),(u,h)=>{const E=y("p-code-highlight"),B=y("p-tabs"),I=y("p-layout-default");return l(s)?(f(),p(I,{key:l(s).id,class:"flow-run"},{header:o(()=>[a(l(ue),{"flow-run-id":l(s).id,onDelete:T},null,8,["flow-run-id"])]),default:o(()=>[v.value?d("",!0):(f(),p(pe,{key:0,"flow-run":l(s)},null,8,["flow-run"])),a(B,{selected:l(i),"onUpdate:selected":h[0]||(h[0]=N=>le(i)?i.value=N:null),tabs:l(w)},{details:o(()=>[a(l(oe),{"flow-run":l(s)},null,8,["flow-run"])]),logs:o(()=>[a(l(ae),{"flow-run":l(s)},null,8,["flow-run"])]),artifacts:o(()=>[a(l(ne),{"flow-run":l(s)},null,8,["flow-run"])]),"task-runs":o(()=>[a(l(se),{"flow-run-id":l(s).id},null,8,["flow-run-id"])]),"subflow-runs":o(()=>[a(l(te),{filter:l(n)},null,8,["filter"])]),parameters:o(()=>[a(l(F),{"text-to-copy":_.value},{default:o(()=>[a(E,{lang:"json",text:_.value,class:"flow-run__parameters"},null,8,["text"])]),_:1},8,["text-to-copy"])]),"job-variables":o(()=>[a(l(F),{"text-to-copy":r.value},{default:o(()=>[a(E,{lang:"json",text:r.value,class:"flow-run__job-variables"},null,8,["text"])]),_:1},8,["text-to-copy"])]),_:1},8,["selected","tabs"])]),_:1})):d("",!0)}}});export{we as default};
//# sourceMappingURL=FlowRun-T0ma01v4.js.map
