{"version": 3, "file": "DeploymentEdit-hkxekFQE.js", "sources": ["../../src/pages/DeploymentEdit.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"deployment\" class=\"deployment-edit\">\n    <template #header>\n      <PageHeadingDeploymentEdit :deployment=\"deployment\" />\n    </template>\n\n    <DeploymentForm :deployment=\"deployment\" @cancel=\"cancel\" @submit=\"submit\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { showToast } from '@prefecthq/prefect-design'\n  import { PageHeadingDeploymentEdit, useWorkspaceApi, DeploymentUpdateV2, getApiErrorMessage, DeploymentForm } from '@prefecthq/prefect-ui-library'\n  import { useSubscription, useRouteParam } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import router, { routes } from '@/router'\n\n  const api = useWorkspaceApi()\n  const deploymentId = useRouteParam('deploymentId')\n  const subscriptionOptions = {\n    interval: 300000,\n  }\n\n  const deploymentSubscription = useSubscription(api.deployments.getDeployment, [deploymentId.value], subscriptionOptions)\n  const deployment = computed(() => deploymentSubscription.response)\n\n  async function submit(request: DeploymentUpdateV2): Promise<void> {\n    try {\n      await api.deployments.updateDeploymentV2(deploymentId.value, request)\n      showToast('Deployment updated', 'success')\n      deploymentSubscription.refresh()\n      router.push(routes.deployment(deploymentId.value))\n    } catch (error) {\n      const message = getApiErrorMessage(error, 'Error updating deployment')\n      showToast(message, 'error')\n      console.warn(error)\n    }\n  }\n\n  function cancel(): void {\n    router.back()\n  }\n\n  const title = computed(() => {\n    if (!deployment.value) {\n      return 'Edit Deployment'\n    }\n    return `Edit Deployment: ${deployment.value.name}`\n  })\n  usePageTitle(title)\n</script>\n\n"], "names": ["api", "useWorkspaceApi", "deploymentId", "useRouteParam", "subscriptionOptions", "deploymentSubscription", "useSubscription", "deployment", "computed", "submit", "request", "showToast", "router", "routes", "error", "message", "getApiErrorMessage", "cancel", "title", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingDeploymentEdit", "DeploymentForm"], "mappings": "yPAkBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAeC,EAAc,cAAc,EAC3CC,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAAyBC,EAAgBN,EAAI,YAAY,cAAe,CAACE,EAAa,KAAK,EAAGE,CAAmB,EACjHG,EAAaC,EAAS,IAAMH,EAAuB,QAAQ,EAEjE,eAAeI,EAAOC,EAA4C,CAC5D,GAAA,CACF,MAAMV,EAAI,YAAY,mBAAmBE,EAAa,MAAOQ,CAAO,EACpEC,EAAU,qBAAsB,SAAS,EACzCN,EAAuB,QAAQ,EAC/BO,EAAO,KAAKC,EAAO,WAAWX,EAAa,KAAK,CAAC,QAC1CY,EAAO,CACR,MAAAC,EAAUC,EAAmBF,EAAO,2BAA2B,EACrEH,EAAUI,EAAS,OAAO,EAC1B,QAAQ,KAAKD,CAAK,CAAA,CACpB,CAGF,SAASG,GAAe,CACtBL,EAAO,KAAK,CAAA,CAGR,MAAAM,EAAQV,EAAS,IAChBD,EAAW,MAGT,oBAAoBA,EAAW,MAAM,IAAI,GAFvC,iBAGV,EACD,OAAAY,EAAaD,CAAK,+CAjDMX,EAAU,WAAlCa,EAMmBC,EAAA,OANiB,MAAM,iBAAA,GAC7B,SACT,IAAsD,CAAtDC,EAAsDC,EAAAC,CAAA,EAAA,CAA1B,WAAYjB,EAAU,OAAA,KAAA,EAAA,CAAA,YAAA,CAAA,CAAA,aAGpD,IAA6E,CAA7Ee,EAA6EC,EAAAE,CAAA,EAAA,CAA5D,WAAYlB,EAAU,MAAG,SAAQU,EAAS,SAAQR,CAAA"}