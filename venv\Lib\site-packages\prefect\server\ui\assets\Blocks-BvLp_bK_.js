import{d as m,f as p,g as d,h as a,i as _,c as s,o as t,j as c,q as f,a as k,F as i,m as o,cf as C,cg as h,k as y,ch as B}from"./index-g6FNXcTE.js";import{u as g}from"./usePageTitle-DEr56mEx.js";const E=m({__name:"Blocks",setup(x){const n=p(),e=d(n.blockDocuments.getBlockDocumentsCount),l=a(()=>e.executed&&e.response==0),r=a(()=>e.executed);return g("Blocks"),(D,b)=>{const u=_("p-layout-default");return t(),s(u,{class:"blocks"},{header:c(()=>[y(o(B))]),default:c(()=>[r.value?(t(),f(i,{key:0},[l.value?(t(),s(o(C),{key:0})):(t(),s(o(h),{key:1,onDelete:o(e).refresh},null,8,["onDelete"]))],64)):k("",!0)]),_:1})}}});export{E as default};
//# sourceMappingURL=Blocks-BvLp_bK_.js.map
