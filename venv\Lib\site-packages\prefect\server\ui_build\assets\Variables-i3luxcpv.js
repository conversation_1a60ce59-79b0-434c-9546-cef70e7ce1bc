import{d as m,ae as d,f as _,h as o,g as v,H as b,i as h,c as r,o as t,j as c,q as k,a as y,F as C,m as n,cE as g,cF as x,k as V,cG as D}from"./index-g6FNXcTE.js";import{u as B}from"./usePageTitle-DEr56mEx.js";const S=m({__name:"Variables",setup(F){const l=d(),a=()=>{var e;s.value.refresh(),(e=l.value)==null||e.refreshSubscriptions()},u=_(),s=o(()=>v(u.variables.getVariables)),i=o(()=>{var e;return s.value.executed&&((e=s.value.response)==null?void 0:e.length)===0}),p=o(()=>s.value.executed);return B(b.info.variables),(e,w)=>{const f=h("p-layout-default");return t(),r(f,{class:"variables"},{header:c(()=>[V(n(D),{onCreate:a})]),default:c(()=>[p.value?(t(),k(C,{key:0},[i.value?(t(),r(n(g),{key:0,onCreate:a})):(t(),r(n(x),{key:1,ref_key:"table",ref:l,onDelete:a,onUpdate:a},null,512))],64)):y("",!0)]),_:1})}}});export{S as default};
//# sourceMappingURL=Variables-i3luxcpv.js.map
