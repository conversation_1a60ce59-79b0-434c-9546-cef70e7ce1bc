import{d as c,f as u,W as _,J as k,i as m,c as d,o as w,j as l,k as r,m as e,d1 as i,d2 as f}from"./index-g6FNXcTE.js";import{u as P}from"./usePageTitle-DEr56mEx.js";const N=c({__name:"WorkPoolEdit",async setup(y){let o,a;const s=u(),n=_("workPoolName"),t=([o,a]=k(()=>s.workPools.getWorkPoolByName(n.value)),o=await o,a(),o);return P("Create Work Pool"),(C,W)=>{const p=m("p-layout-default");return w(),d(p,null,{header:l(()=>[r(e(f),{"work-pool":e(t)},null,8,["work-pool"])]),default:l(()=>[r(e(i),{"work-pool":e(t)},null,8,["work-pool"])]),_:1})}}});export{N as default};
//# sourceMappingURL=WorkPoolEdit-BKxgBpkD.js.map
