import{d as h,f as x,W as g,u as D,h as i,aI as I,al as N,bt as R,g as S,i as u,c,o as n,j as a,k as T,m as t,a6 as B,a as r,cw as F,cx as m,cy as V,be as W}from"./index-g6FNXcTE.js";import{u as j}from"./usePageTitle-DEr56mEx.js";const Z=h({__name:"ConcurrencyLimit",setup(A){const p=x(),y=g("concurrencyLimitId"),d=D(),v=i(()=>[{label:"Details",hidden:I.xl},{label:"Active Task Runs"}]),s=N("tab","Details"),{tabs:_}=R(v,s),b={interval:3e5},L=S(p.concurrencyLimits.getConcurrencyLimit,[y.value],b),e=i(()=>L.response);function f(){d.push(W.concurrencyLimits())}const C=i(()=>e.value?`Concurrency Limit: ${e.value.tag}`:"Concurrency Limit");return j(C),(O,l)=>{const k=u("p-tabs"),w=u("p-layout-well");return n(),c(w,{class:"concurrencyLimit"},{header:a(()=>[e.value?(n(),c(t(V),{key:0,"concurrency-limit":e.value,onDelete:f},null,8,["concurrency-limit"])):r("",!0)]),well:a(()=>[e.value?(n(),c(t(m),{key:0,alternate:"","concurrency-limit":e.value},null,8,["concurrency-limit"])):r("",!0)]),default:a(()=>[T(k,{selected:t(s),"onUpdate:selected":l[0]||(l[0]=o=>B(s)?s.value=o:null),tabs:t(_)},{details:a(()=>[e.value?(n(),c(t(m),{key:0,"concurrency-limit":e.value},null,8,["concurrency-limit"])):r("",!0)]),"active-task-runs":a(()=>{var o;return[(o=e.value)!=null&&o.activeSlots?(n(),c(t(F),{key:0,"active-slots":e.value.activeSlots},null,8,["active-slots"])):r("",!0)]}),_:1},8,["selected","tabs"])]),_:1})}}});export{Z as default};
//# sourceMappingURL=ConcurrencyLimit-BGQGeqFk.js.map
