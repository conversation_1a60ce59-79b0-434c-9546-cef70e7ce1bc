{"version": 3, "file": "BlocksCatalog-DTEScudj.js", "sources": ["../../src/pages/BlocksCatalog.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"blocks-catalog\">\n    <template #header>\n      <PageHeadingBlocksCatalog />\n    </template>\n\n    <BlockTypeList v-model:capability=\"capability\" :block-types=\"blockTypes\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeadingBlocksCatalog, BlockTypeList, useWorkspaceApi, useBlockTypesFilter } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { computed, ref } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const capability = ref<string | null>(null)\n  const blockCapabilities = computed(() => capability.value ? [capability.value] : [])\n\n  const { filter } = useBlockTypesFilter({\n    blockSchemas: {\n      blockCapabilities,\n    },\n  })\n  const blockTypesSubscription = useSubscription(api.blockTypes.getBlockTypes, [filter])\n  const blockTypes = computed(() => blockTypesSubscription.response ?? [])\n\n  usePageTitle('Blocks Catalog')\n</script>"], "names": ["api", "useWorkspaceApi", "capability", "ref", "blockCapabilities", "computed", "filter", "useBlockTypesFilter", "blockTypesSubscription", "useSubscription", "blockTypes", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingBlocksCatalog", "BlockTypeList", "$event"], "mappings": "4NAgBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAaC,EAAmB,IAAI,EACpCC,EAAoBC,EAAS,IAAMH,EAAW,MAAQ,CAACA,EAAW,KAAK,EAAI,EAAE,EAE7E,CAAE,OAAAI,CAAO,EAAIC,EAAoB,CACrC,aAAc,CACZ,kBAAAH,CAAA,CACF,CACD,EACKI,EAAyBC,EAAgBT,EAAI,WAAW,cAAe,CAACM,CAAM,CAAC,EAC/EI,EAAaL,EAAS,IAAMG,EAAuB,UAAY,CAAA,CAAE,EAEvE,OAAAG,EAAa,gBAAgB,iDA3B7B,EAAAC,EAMmBC,EAAA,CAND,MAAM,kBAAgB,CAC3B,SACT,IAA4B,CAA5BC,EAA4BC,EAAAC,CAAA,CAAA,CAAA,aAG9B,IAA2E,CAA3EF,EAA2EC,EAAAE,CAAA,EAAA,CAApD,WAAYf,EAAU,2CAAVA,EAAU,MAAAgB,GAAG,cAAaR,EAAU"}